import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة إدارة المستخدمين والمصادقة
class UserService {
  static final UserService _instance = UserService._internal();
  factory UserService() => _instance;
  UserService._internal();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// الحصول على معرف المستخدم الحالي
  String? getCurrentUserId() {
    final user = _auth.currentUser;
    if (user != null) {
      return user.uid;
    }

    // في حالة عدم وجود مستخدم مسجل، استخدم معرف افتراضي للاختبار
    return 'demo_user_${DateTime.now().millisecondsSinceEpoch % 1000}';
  }

  /// الحصول على بيانات المستخدم الحالي
  Future<Map<String, dynamic>?> getCurrentUserData() async {
    try {
      final userId = getCurrentUserId();
      if (userId == null) return null;

      final userDoc = await _firestore
          .collection('users')
          .doc(userId)
          .get();

      if (userDoc.exists) {
        return userDoc.data();
      }

      // إنشاء بيانات مستخدم افتراضية إذا لم تكن موجودة
      return await _createDefaultUserData(userId);
    } catch (e) {
      print('❌ خطأ في جلب بيانات المستخدم: $e');
      return null;
    }
  }

  /// إنشاء بيانات مستخدم افتراضية
  Future<Map<String, dynamic>> _createDefaultUserData(String userId) async {
    final userData = {
      'id': userId,
      'name': 'مستخدم تجريبي',
      'email': '<EMAIL>',
      'phone': '+966500000000',
      'location': 'الرياض، المملكة العربية السعودية',
      'farmName': 'مزرعة تجريبية',
      'farmSize': 1000.0, // متر مربع
      'cropTypes': ['خضروات', 'فواكه'],
      'irrigationExperience': 'متوسط',
      'createdAt': Timestamp.fromDate(DateTime.now()),
      'lastLogin': Timestamp.fromDate(DateTime.now()),
      'isActive': true,
      'preferences': {
        'language': 'ar',
        'notifications': true,
        'autoIrrigation': true,
        'smartMode': true,
        'theme': 'light',
      },
      'subscription': {
        'type': 'free',
        'startDate': Timestamp.fromDate(DateTime.now()),
        'endDate': Timestamp.fromDate(DateTime.now().add(const Duration(days: 30))),
        'maxSystems': 3,
        'features': ['basic_monitoring', 'manual_irrigation', 'basic_reports'],
      },
    };

    try {
      await _firestore
          .collection('users')
          .doc(userId)
          .set(userData);
      
      print('✅ تم إنشاء بيانات مستخدم افتراضية: $userId');
      return userData;
    } catch (e) {
      print('❌ خطأ في إنشاء بيانات المستخدم: $e');
      return userData;
    }
  }

  /// تحديث آخر تسجيل دخول
  Future<void> updateLastLogin() async {
    try {
      final userId = getCurrentUserId();
      if (userId == null) return;

      await _firestore
          .collection('users')
          .doc(userId)
          .update({
        'lastLogin': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      print('❌ خطأ في تحديث آخر تسجيل دخول: $e');
    }
  }

  /// تحديث تفضيلات المستخدم
  Future<void> updateUserPreferences(Map<String, dynamic> preferences) async {
    try {
      final userId = getCurrentUserId();
      if (userId == null) return;

      await _firestore
          .collection('users')
          .doc(userId)
          .update({
        'preferences': preferences,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });

      // حفظ التفضيلات محلياً أيضاً
      final prefs = await SharedPreferences.getInstance();
      for (final entry in preferences.entries) {
        if (entry.value is bool) {
          await prefs.setBool('pref_${entry.key}', entry.value);
        } else if (entry.value is String) {
          await prefs.setString('pref_${entry.key}', entry.value);
        } else if (entry.value is int) {
          await prefs.setInt('pref_${entry.key}', entry.value);
        } else if (entry.value is double) {
          await prefs.setDouble('pref_${entry.key}', entry.value);
        }
      }

      print('✅ تم تحديث تفضيلات المستخدم');
    } catch (e) {
      print('❌ خطأ في تحديث تفضيلات المستخدم: $e');
    }
  }

  /// الحصول على تفضيلات المستخدم
  Future<Map<String, dynamic>> getUserPreferences() async {
    try {
      final userData = await getCurrentUserData();
      if (userData != null && userData['preferences'] != null) {
        return Map<String, dynamic>.from(userData['preferences']);
      }

      // إرجاع تفضيلات افتراضية
      return {
        'language': 'ar',
        'notifications': true,
        'autoIrrigation': true,
        'smartMode': true,
        'theme': 'light',
      };
    } catch (e) {
      print('❌ خطأ في جلب تفضيلات المستخدم: $e');
      return {
        'language': 'ar',
        'notifications': true,
        'autoIrrigation': true,
        'smartMode': true,
        'theme': 'light',
      };
    }
  }

  /// فحص صلاحيات المستخدم
  Future<bool> hasPermission(String feature) async {
    try {
      final userData = await getCurrentUserData();
      if (userData == null) return false;

      final subscription = userData['subscription'] as Map<String, dynamic>?;
      if (subscription == null) return false;

      final features = List<String>.from(subscription['features'] ?? []);
      return features.contains(feature);
    } catch (e) {
      print('❌ خطأ في فحص الصلاحيات: $e');
      return false;
    }
  }

  /// الحصول على الحد الأقصى لعدد الأنظمة
  Future<int> getMaxSystemsLimit() async {
    try {
      final userData = await getCurrentUserData();
      if (userData == null) return 3; // افتراضي

      final subscription = userData['subscription'] as Map<String, dynamic>?;
      if (subscription == null) return 3;

      return subscription['maxSystems'] ?? 3;
    } catch (e) {
      print('❌ خطأ في جلب حد الأنظمة: $e');
      return 3;
    }
  }

  /// فحص انتهاء الاشتراك
  Future<bool> isSubscriptionActive() async {
    try {
      final userData = await getCurrentUserData();
      if (userData == null) return false;

      final subscription = userData['subscription'] as Map<String, dynamic>?;
      if (subscription == null) return false;

      final endDate = (subscription['endDate'] as Timestamp?)?.toDate();
      if (endDate == null) return false;

      return DateTime.now().isBefore(endDate);
    } catch (e) {
      print('❌ خطأ في فحص الاشتراك: $e');
      return false;
    }
  }

  /// تسجيل الخروج
  Future<void> signOut() async {
    try {
      await _auth.signOut();
      
      // مسح البيانات المحلية
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
      
      print('✅ تم تسجيل الخروج بنجاح');
    } catch (e) {
      print('❌ خطأ في تسجيل الخروج: $e');
    }
  }

  /// مراقبة حالة المصادقة
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  /// فحص حالة الاتصال بالإنترنت
  Future<bool> checkInternetConnection() async {
    try {
      // محاولة الوصول لـ Firestore
      await _firestore
          .collection('test')
          .limit(1)
          .get()
          .timeout(const Duration(seconds: 5));
      return true;
    } catch (e) {
      return false;
    }
  }

  /// إحصائيات المستخدم
  Future<Map<String, dynamic>> getUserStats() async {
    try {
      final userId = getCurrentUserId();
      if (userId == null) return {};

      // جلب عدد الأنظمة
      final systemsSnapshot = await _firestore
          .collection('irrigation_systems')
          .where('userId', isEqualTo: userId)
          .get();

      // جلب عدد السجلات
      final recordsSnapshot = await _firestore
          .collection('irrigation_records')
          .where('systemId', whereIn: systemsSnapshot.docs.map((doc) => doc.id).toList())
          .get();

      return {
        'totalSystems': systemsSnapshot.docs.length,
        'totalRecords': recordsSnapshot.docs.length,
        'joinDate': (await getCurrentUserData())?['createdAt'],
        'lastActivity': (await getCurrentUserData())?['lastLogin'],
      };
    } catch (e) {
      print('❌ خطأ في جلب إحصائيات المستخدم: $e');
      return {};
    }
  }
}
