import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:sam05/models/app_models.dart';
import 'package:sam05/repositories/irrigation_repository.dart';
import 'package:sam05/services/smart_irrigation_service.dart';
import 'package:sam05/services/user_service.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

// ===== الأحداث (Events) =====

/// الحدث الأساسي لنظام الري
abstract class IrrigationEvent extends Equatable {
  const IrrigationEvent();

  @override
  List<Object?> get props => [];
}

/// تحميل أنظمة الري
class IrrigationEventLoadSystems extends IrrigationEvent {
  final String? userId;

  const IrrigationEventLoadSystems(this.userId);

  @override
  List<Object?> get props => [userId];
}

/// تحديد نظام الري النشط
class IrrigationEventSelectSystem extends IrrigationEvent {
  final String systemId;

  const IrrigationEventSelectSystem(this.systemId);

  @override
  List<Object?> get props => [systemId];
}

/// تحديث بيانات الحساسات
class IrrigationEventUpdateSensors extends IrrigationEvent {
  final String systemId;

  const IrrigationEventUpdateSensors(this.systemId);

  @override
  List<Object?> get props => [systemId];
}

/// بدء الري
class IrrigationEventStartIrrigation extends IrrigationEvent {
  final String systemId;
  final IrrigationTrigger trigger;
  final int? duration; // بالدقائق، null للمدة الافتراضية

  const IrrigationEventStartIrrigation({
    required this.systemId,
    required this.trigger,
    this.duration,
  });

  @override
  List<Object?> get props => [systemId, trigger, duration];
}

/// إيقاف الري
class IrrigationEventStopIrrigation extends IrrigationEvent {
  final String systemId;

  const IrrigationEventStopIrrigation(this.systemId);

  @override
  List<Object?> get props => [systemId];
}

/// تحديث إعدادات الري
class IrrigationEventUpdateSettings extends IrrigationEvent {
  final IrrigationSettings settings;

  const IrrigationEventUpdateSettings(this.settings);

  @override
  List<Object?> get props => [settings];
}

/// تحميل سجلات الري
class IrrigationEventLoadRecords extends IrrigationEvent {
  final String systemId;

  const IrrigationEventLoadRecords(this.systemId);

  @override
  List<Object?> get props => [systemId];
}

/// تحميل إحصائيات الري
class IrrigationEventLoadStats extends IrrigationEvent {
  final String systemId;
  final DateTime startDate;
  final DateTime endDate;

  const IrrigationEventLoadStats({
    required this.systemId,
    required this.startDate,
    required this.endDate,
  });

  @override
  List<Object?> get props => [systemId, startDate, endDate];
}

/// حدث بدء الري بنوع محدد
class IrrigationEventStartIrrigationWithType extends IrrigationEvent {
  final String systemId;
  final IrrigationType type;
  final int duration;

  const IrrigationEventStartIrrigationWithType({
    required this.systemId,
    required this.type,
    required this.duration,
  });

  @override
  List<Object> get props => [systemId, type, duration];
}

/// حدث تحميل سجل الري
class IrrigationEventLoadLogs extends IrrigationEvent {
  final String systemId;
  final int limit;

  const IrrigationEventLoadLogs({
    required this.systemId,
    this.limit = 20,
  });

  @override
  List<Object> get props => [systemId, limit];
}

/// حدث بدء المراقبة التلقائية
class IrrigationEventStartMonitoring extends IrrigationEvent {
  final String userId;

  const IrrigationEventStartMonitoring(this.userId);

  @override
  List<Object> get props => [userId];
}

/// حدث إيقاف المراقبة التلقائية
class IrrigationEventStopMonitoring extends IrrigationEvent {
  const IrrigationEventStopMonitoring();

  @override
  List<Object> get props => [];
}

/// حدث إضافة نظام ري جديد
class IrrigationEventAddSystem extends IrrigationEvent {
  final String name;
  final String type;
  final String serialNumber;
  final String? description;
  final String? location;

  const IrrigationEventAddSystem({
    required this.name,
    required this.type,
    required this.serialNumber,
    this.description,
    this.location,
  });

  @override
  List<Object?> get props => [name, type, serialNumber, description, location];
}

/// بدء مراقبة Real-time للحساسات
class IrrigationEventStartRealtimeMonitoring extends IrrigationEvent {
  final String systemId;

  const IrrigationEventStartRealtimeMonitoring(this.systemId);

  @override
  List<Object?> get props => [systemId];
}

/// إيقاف مراقبة Real-time للحساسات
class IrrigationEventStopRealtimeMonitoring extends IrrigationEvent {
  final String systemId;

  const IrrigationEventStopRealtimeMonitoring(this.systemId);

  @override
  List<Object?> get props => [systemId];
}

/// تحديث Real-time للحساسات
class IrrigationEventRealtimeSensorUpdate extends IrrigationEvent {
  final String systemId;
  final SensorData sensorData;

  const IrrigationEventRealtimeSensorUpdate(this.systemId, this.sensorData);

  @override
  List<Object?> get props => [systemId, sensorData];
}

// ===== الحالات (States) =====

/// الحالة الأساسية لنظام الري
abstract class IrrigationState extends Equatable {
  const IrrigationState();

  @override
  List<Object?> get props => [];
}

/// الحالة الأولية
class IrrigationStateInitial extends IrrigationState {}

/// حالة التحميل
class IrrigationStateLoading extends IrrigationState {}

/// حالة تحميل أنظمة الري بنجاح
class IrrigationStateSystemsLoaded extends IrrigationState {
  final List<IrrigationSystem> systems;
  final String? selectedSystemId;

  const IrrigationStateSystemsLoaded({
    required this.systems,
    this.selectedSystemId,
  });

  @override
  List<Object?> get props => [systems, selectedSystemId];
}

/// حالة تحديد نظام الري
class IrrigationStateSystemSelected extends IrrigationState {
  final IrrigationSystem system;
  final IrrigationSettings? settings;
  final bool isIrrigating;

  const IrrigationStateSystemSelected({
    required this.system,
    this.settings,
    this.isIrrigating = false,
  });

  @override
  List<Object?> get props => [system, settings, isIrrigating];
}

/// حالة تحديث الحساسات
class IrrigationStateSensorsUpdated extends IrrigationState {
  final IrrigationSystem system;
  final SensorData sensorData;

  const IrrigationStateSensorsUpdated({
    required this.system,
    required this.sensorData,
  });

  @override
  List<Object?> get props => [system, sensorData];
}

/// حالة بدء الري
class IrrigationStateIrrigationStarted extends IrrigationState {
  final String systemId;
  final IrrigationRecord record;

  const IrrigationStateIrrigationStarted({
    required this.systemId,
    required this.record,
  });

  @override
  List<Object?> get props => [systemId, record];
}

/// حالة إيقاف الري
class IrrigationStateIrrigationStopped extends IrrigationState {
  final String systemId;
  final IrrigationRecord record;

  const IrrigationStateIrrigationStopped({
    required this.systemId,
    required this.record,
  });

  @override
  List<Object?> get props => [systemId, record];
}

/// حالة تحميل السجلات
class IrrigationStateRecordsLoaded extends IrrigationState {
  final List<IrrigationRecord> records;

  const IrrigationStateRecordsLoaded(this.records);

  @override
  List<Object?> get props => [records];
}

/// حالة تحميل الإحصائيات
class IrrigationStateStatsLoaded extends IrrigationState {
  final Map<String, dynamic> stats;

  const IrrigationStateStatsLoaded(this.stats);

  @override
  List<Object?> get props => [stats];
}

/// حالة تحديث الإعدادات
class IrrigationStateSettingsUpdated extends IrrigationState {
  final IrrigationSettings settings;

  const IrrigationStateSettingsUpdated(this.settings);

  @override
  List<Object?> get props => [settings];
}

/// حالة تحميل سجل الري
class IrrigationStateLogsLoaded extends IrrigationState {
  final List<IrrigationLog> logs;

  const IrrigationStateLogsLoaded(this.logs);

  @override
  List<Object?> get props => [logs];
}

/// حالة بدء المراقبة
class IrrigationStateMonitoringStarted extends IrrigationState {
  final String userId;

  const IrrigationStateMonitoringStarted(this.userId);

  @override
  List<Object?> get props => [userId];
}

/// حالة إيقاف المراقبة
class IrrigationStateMonitoringStopped extends IrrigationState {
  const IrrigationStateMonitoringStopped();

  @override
  List<Object?> get props => [];
}

/// حالة الخطأ
class IrrigationStateError extends IrrigationState {
  final String message;

  const IrrigationStateError(this.message);

  @override
  List<Object?> get props => [message];
}

/// حالة بدء مراقبة Real-time
class IrrigationStateRealtimeMonitoringStarted extends IrrigationState {
  final String systemId;

  const IrrigationStateRealtimeMonitoringStarted(this.systemId);

  @override
  List<Object?> get props => [systemId];
}

/// حالة إيقاف مراقبة Real-time
class IrrigationStateRealtimeMonitoringStopped extends IrrigationState {
  final String systemId;

  const IrrigationStateRealtimeMonitoringStopped(this.systemId);

  @override
  List<Object?> get props => [systemId];
}

/// حالة تحديث Real-time للحساسات
class IrrigationStateRealtimeSensorUpdate extends IrrigationState {
  final String systemId;
  final SensorData sensorData;
  final DateTime timestamp;

  const IrrigationStateRealtimeSensorUpdate({
    required this.systemId,
    required this.sensorData,
    required this.timestamp,
  });

  @override
  List<Object?> get props => [systemId, sensorData, timestamp];
}

// ===== BLoC =====

/// BLoC لإدارة نظام الري الذكي
class IrrigationBloc extends Bloc<IrrigationEvent, IrrigationState> {
  final IrrigationRepository irrigationRepository;
  final SmartIrrigationService _smartIrrigationService =
      SmartIrrigationService();

  // متغيرات لتتبع الحالة الحالية
  final List<IrrigationSystem> _systems = [];
  String? _selectedSystemId; // تتبع نظام الري النشط
  final Map<String, bool> _irrigationStatus = {}; // تتبع حالة الري لكل نظام
  StreamSubscription<QuerySnapshot>? _sensorsSubscription;
  String? _currentUserId; // تخزين معرف المستخدم الحالي
  final Map<String, StreamSubscription<SensorData>>
      _realtimeSensorSubscriptions = {};
  final UserService _userService = UserService();

  IrrigationBloc({
    required this.irrigationRepository,
  }) : super(IrrigationStateInitial()) {
    on<IrrigationEventLoadSystems>(_onLoadSystems);
    on<IrrigationEventSelectSystem>(_onSelectSystem);
    // on<IrrigationEventUpdateSensors>(_onUpdateSensors); // محذوف: تحديث الحساسات من الأجهزة فقط
    on<IrrigationEventStartIrrigation>(_onStartIrrigation);
    on<IrrigationEventStopIrrigation>(_onStopIrrigation);
    on<IrrigationEventUpdateSettings>(_onUpdateSettings);
    on<IrrigationEventLoadRecords>(_onLoadRecords);
    on<IrrigationEventLoadStats>(_onLoadStats);
    on<IrrigationEventStartIrrigationWithType>(_onStartIrrigationWithType);
    on<IrrigationEventLoadLogs>(_onLoadLogs);
    on<IrrigationEventStartMonitoring>(_onStartMonitoring);
    on<IrrigationEventStopMonitoring>(_onStopMonitoring);
    on<IrrigationEventAddSystem>(_onAddSystem);
    on<IrrigationEventStartRealtimeMonitoring>(_onStartRealtimeMonitoring);
    on<IrrigationEventStopRealtimeMonitoring>(_onStopRealtimeMonitoring);
    on<IrrigationEventRealtimeSensorUpdate>(_onRealtimeSensorUpdate);
  }

  /// الحصول على معرف المستخدم الحالي
  Future<String?> _getCurrentUserId() async {
    // استخدام UserService للحصول على معرف المستخدم الحقيقي
    return _currentUserId ?? _userService.getCurrentUserId() ?? 'demo_user';
  }

  /// تعيين معرف المستخدم الحالي
  void setCurrentUserId(String userId) {
    _currentUserId = userId;
  }

  @override
  Future<void> close() {
    _sensorsSubscription?.cancel();
    _smartIrrigationService.stopMonitoring();

    // إيقاف جميع اشتراكات Real-time
    for (final subscription in _realtimeSensorSubscriptions.values) {
      subscription.cancel();
    }
    _realtimeSensorSubscriptions.clear();

    // تنظيف موارد Repository
    irrigationRepository.dispose();

    return super.close();
  }

  /// تحميل أنظمة الري
  Future<void> _onLoadSystems(
    IrrigationEventLoadSystems event,
    Emitter<IrrigationState> emit,
  ) async {
    try {
      emit(IrrigationStateLoading());

      _systems.addAll(
          await irrigationRepository.getIrrigationSystems(event.userId));

      emit(IrrigationStateSystemsLoaded(
        systems: _systems,
        selectedSystemId: _selectedSystemId,
      ));
    } catch (e) {
      emit(IrrigationStateError('فشل في تحميل أنظمة الري: ${e.toString()}'));
    }
  }

  /// تحديد نظام الري النشط
  Future<void> _onSelectSystem(
    IrrigationEventSelectSystem event,
    Emitter<IrrigationState> emit,
  ) async {
    try {
      emit(IrrigationStateLoading());

      _selectedSystemId = event.systemId;

      final system =
          await irrigationRepository.getIrrigationSystem(event.systemId);
      if (system == null) {
        emit(const IrrigationStateError('لم يتم العثور على النظام المحدد'));
        return;
      }

      final settings =
          await irrigationRepository.getIrrigationSettings(event.systemId);
      final isIrrigating = _irrigationStatus[event.systemId] ?? false;

      emit(IrrigationStateSystemSelected(
        system: system,
        settings: settings,
        isIrrigating: isIrrigating,
      ));
    } catch (e) {
      emit(IrrigationStateError('فشل في تحديد النظام: ${e.toString()}'));
    }
  }

  /// ملاحظة: تحديث الحساسات يتم من الأجهزة الفعلية فقط
  /// هذا الحدث لم يعد مستخدماً
  @Deprecated('تحديث الحساسات يتم من الأجهزة الفعلية فقط')
  Future<void> _onUpdateSensors(
    IrrigationEventUpdateSensors event,
    Emitter<IrrigationState> emit,
  ) async {
    emit(const IrrigationStateError(
        'تحديث الحساسات يتم من الأجهزة الفعلية فقط، وليس من التطبيق'));
  }

  /// بدء الري
  Future<void> _onStartIrrigation(
    IrrigationEventStartIrrigation event,
    Emitter<IrrigationState> emit,
  ) async {
    try {
      final system =
          await irrigationRepository.getIrrigationSystem(event.systemId);
      if (system == null) {
        emit(const IrrigationStateError('لم يتم العثور على النظام المحدد'));
        return;
      }

      final settings =
          await irrigationRepository.getIrrigationSettings(event.systemId);
      final duration = event.duration ?? settings?.defaultDuration ?? 30;

      // إنشاء سجل ري جديد
      final record = await irrigationRepository.addIrrigationRecord(
        systemId: event.systemId,
        startTime: DateTime.now(),
        duration: duration,
        waterUsed: duration * 5.0, // تقدير: 5 لتر في الدقيقة
        trigger: event.trigger,
        sensorDataAtStart: system.sensors.toMap(),
      );

      // تحديث حالة الري
      _irrigationStatus[event.systemId] = true;

      emit(IrrigationStateIrrigationStarted(
        systemId: event.systemId,
        record: record,
      ));

      // محاكاة إيقاف الري تلقائياً بعد المدة المحددة
      Future.delayed(const Duration(seconds: 5), () {
        add(IrrigationEventStopIrrigation(event.systemId));
      });
    } catch (e) {
      emit(IrrigationStateError('فشل في بدء الري: ${e.toString()}'));
    }
  }

  /// إيقاف الري
  Future<void> _onStopIrrigation(
    IrrigationEventStopIrrigation event,
    Emitter<IrrigationState> emit,
  ) async {
    try {
      // تحديث حالة الري
      _irrigationStatus[event.systemId] = false;

      // في التطبيق الحقيقي، سنحدث سجل الري بوقت الانتهاء الفعلي
      final now = DateTime.now();

      // محاكاة سجل ري مكتمل
      final record = await irrigationRepository.addIrrigationRecord(
        systemId: event.systemId,
        startTime: now.subtract(const Duration(minutes: 5)),
        endTime: now,
        duration: 5,
        waterUsed: 25.0,
        trigger: IrrigationTrigger.manual,
        notes: 'تم إيقاف الري يدوياً',
      );

      emit(IrrigationStateIrrigationStopped(
        systemId: event.systemId,
        record: record,
      ));
    } catch (e) {
      emit(IrrigationStateError('فشل في إيقاف الري: ${e.toString()}'));
    }
  }

  /// تحديث إعدادات الري
  Future<void> _onUpdateSettings(
    IrrigationEventUpdateSettings event,
    Emitter<IrrigationState> emit,
  ) async {
    try {
      await irrigationRepository.saveIrrigationSettings(event.settings);
      emit(IrrigationStateSettingsUpdated(event.settings));
    } catch (e) {
      emit(IrrigationStateError('فشل في تحديث الإعدادات: ${e.toString()}'));
    }
  }

  /// تحميل سجلات الري
  Future<void> _onLoadRecords(
    IrrigationEventLoadRecords event,
    Emitter<IrrigationState> emit,
  ) async {
    try {
      emit(IrrigationStateLoading());
      final records =
          await irrigationRepository.getIrrigationRecords(event.systemId);
      emit(IrrigationStateRecordsLoaded(records));
    } catch (e) {
      emit(IrrigationStateError('فشل في تحميل السجلات: ${e.toString()}'));
    }
  }

  /// تحميل إحصائيات الري
  Future<void> _onLoadStats(
    IrrigationEventLoadStats event,
    Emitter<IrrigationState> emit,
  ) async {
    try {
      emit(IrrigationStateLoading());
      final stats = await irrigationRepository.getWaterUsageStats(
        event.systemId,
        startDate: event.startDate,
        endDate: event.endDate,
      );
      emit(IrrigationStateStatsLoaded(stats));
    } catch (e) {
      emit(IrrigationStateError('فشل في تحميل الإحصائيات: ${e.toString()}'));
    }
  }

  // ===== دوال مساعدة =====

  /// التحقق من حالة الري لنظام محدد
  bool isSystemIrrigating(String systemId) {
    return _irrigationStatus[systemId] ?? false;
  }

  /// الحصول على النظام المحدد حالياً
  IrrigationSystem? get selectedSystem {
    if (_selectedSystemId == null) return null;
    try {
      return _systems.firstWhere((system) => system.id == _selectedSystemId);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على جميع الأنظمة
  List<IrrigationSystem> get systems => List.unmodifiable(_systems);

  /// بدء الري بنوع محدد
  Future<void> _onStartIrrigationWithType(
    IrrigationEventStartIrrigationWithType event,
    Emitter<IrrigationState> emit,
  ) async {
    try {
      // الحصول على معرف المستخدم الحالي من الحالة المصادق عليها
      final currentUserId = await _getCurrentUserId();
      if (currentUserId == null) {
        emit(const IrrigationStateError('لم يتم العثور على معرف المستخدم'));
        return;
      }

      await _smartIrrigationService.startIrrigation(
        systemId: event.systemId,
        userId: currentUserId,
        type: event.type,
        duration: event.duration,
      );

      emit(IrrigationStateIrrigationStarted(
        systemId: event.systemId,
        record: IrrigationRecord(
          id: '',
          systemId: event.systemId,
          startTime: DateTime.now(),
          duration: event.duration,
          waterUsed: event.duration * 2.0,
          trigger: event.type == IrrigationType.manual
              ? IrrigationTrigger.manual
              : IrrigationTrigger.auto,
          sensorDataAtStart: const <String, dynamic>{},
        ),
      ));
    } catch (e) {
      emit(IrrigationStateError('فشل في بدء الري: ${e.toString()}'));
    }
  }

  /// تحميل سجل الري
  Future<void> _onLoadLogs(
    IrrigationEventLoadLogs event,
    Emitter<IrrigationState> emit,
  ) async {
    try {
      emit(IrrigationStateLoading());

      final snapshot = await FirebaseFirestore.instance
          .collection('irrigation_logs')
          .where('systemId', isEqualTo: event.systemId)
          .orderBy('startTime', descending: true)
          .limit(event.limit)
          .get();

      final logs =
          snapshot.docs.map((doc) => IrrigationLog.fromFirestore(doc)).toList();

      emit(IrrigationStateLogsLoaded(logs));
    } catch (e) {
      emit(IrrigationStateError('فشل في تحميل سجل الري: ${e.toString()}'));
    }
  }

  /// بدء المراقبة التلقائية
  Future<void> _onStartMonitoring(
    IrrigationEventStartMonitoring event,
    Emitter<IrrigationState> emit,
  ) async {
    try {
      _smartIrrigationService.startMonitoring(event.userId);
      emit(IrrigationStateMonitoringStarted(event.userId));
    } catch (e) {
      emit(IrrigationStateError('فشل في بدء المراقبة: ${e.toString()}'));
    }
  }

  /// إيقاف المراقبة التلقائية
  Future<void> _onStopMonitoring(
    IrrigationEventStopMonitoring event,
    Emitter<IrrigationState> emit,
  ) async {
    try {
      _smartIrrigationService.stopMonitoring();
      emit(const IrrigationStateMonitoringStopped());
    } catch (e) {
      emit(IrrigationStateError('فشل في إيقاف المراقبة: ${e.toString()}'));
    }
  }

  /// إضافة نظام ري جديد
  Future<void> _onAddSystem(
    IrrigationEventAddSystem event,
    Emitter<IrrigationState> emit,
  ) async {
    try {
      emit(IrrigationStateLoading());

      // الحصول على معرف المستخدم الحالي
      final currentUserId = await _getCurrentUserId();
      if (currentUserId == null) {
        emit(const IrrigationStateError('لم يتم العثور على معرف المستخدم'));
        return;
      }

      final newSystem = await irrigationRepository.addIrrigationSystem(
        userId: currentUserId,
        name: event.name,
        type: event.type,
        serialNumber: event.serialNumber,
        description: event.description,
        location: event.location,
      );

      // إضافة النظام الجديد إلى القائمة المحلية
      _systems.add(newSystem);

      emit(IrrigationStateSystemsLoaded(
        systems: _systems,
        selectedSystemId: _selectedSystemId,
      ));
    } catch (e) {
      emit(IrrigationStateError('فشل في إضافة النظام: ${e.toString()}'));
    }
  }

  /// بدء مراقبة Real-time للحساسات
  Future<void> _onStartRealtimeMonitoring(
    IrrigationEventStartRealtimeMonitoring event,
    Emitter<IrrigationState> emit,
  ) async {
    try {
      // إيقاف المراقبة السابقة إن وجدت
      _realtimeSensorSubscriptions[event.systemId]?.cancel();

      // بدء مراقبة جديدة
      final sensorStream =
          irrigationRepository.getSensorDataStream(event.systemId);

      _realtimeSensorSubscriptions[event.systemId] = sensorStream.listen(
        (sensorData) {
          // إرسال تحديث Real-time
          add(IrrigationEventRealtimeSensorUpdate(event.systemId, sensorData));
        },
        onError: (error) {
          emit(IrrigationStateError('خطأ في مراقبة الحساسات: $error'));
        },
      );

      emit(IrrigationStateRealtimeMonitoringStarted(event.systemId));
    } catch (e) {
      emit(IrrigationStateError('فشل في بدء مراقبة الحساسات: ${e.toString()}'));
    }
  }

  /// إيقاف مراقبة Real-time للحساسات
  Future<void> _onStopRealtimeMonitoring(
    IrrigationEventStopRealtimeMonitoring event,
    Emitter<IrrigationState> emit,
  ) async {
    try {
      _realtimeSensorSubscriptions[event.systemId]?.cancel();
      _realtimeSensorSubscriptions.remove(event.systemId);

      irrigationRepository.stopSensorMonitoring(event.systemId);

      emit(IrrigationStateRealtimeMonitoringStopped(event.systemId));
    } catch (e) {
      emit(IrrigationStateError(
          'فشل في إيقاف مراقبة الحساسات: ${e.toString()}'));
    }
  }

  /// معالجة تحديث Real-time للحساسات
  Future<void> _onRealtimeSensorUpdate(
    IrrigationEventRealtimeSensorUpdate event,
    Emitter<IrrigationState> emit,
  ) async {
    try {
      emit(IrrigationStateRealtimeSensorUpdate(
        systemId: event.systemId,
        sensorData: event.sensorData,
        timestamp: DateTime.now(),
      ));
    } catch (e) {
      emit(IrrigationStateError(
          'فشل في معالجة تحديث الحساسات: ${e.toString()}'));
    }
  }

  /// بدء مراقبة Real-time لنظام محدد
  void startRealtimeMonitoring(String systemId) {
    add(IrrigationEventStartRealtimeMonitoring(systemId));
  }

  /// إيقاف مراقبة Real-time لنظام محدد
  void stopRealtimeMonitoring(String systemId) {
    add(IrrigationEventStopRealtimeMonitoring(systemId));
  }

  /// إيقاف جميع مراقبات Real-time
  void stopAllRealtimeMonitoring() {
    for (final systemId in _realtimeSensorSubscriptions.keys.toList()) {
      stopRealtimeMonitoring(systemId);
    }
  }
}
