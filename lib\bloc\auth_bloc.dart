import 'package:flutter_bloc/flutter_bloc.dart';
import "package:equatable/equatable.dart";
import 'package:shared_preferences/shared_preferences.dart';
import "package:firebase_auth/firebase_auth.dart" as firebase_auth;
import '../repositories/auth_repository.dart';
import '../models/app_models.dart';
abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

class AuthEventInitial extends AuthEvent {}

class AuthEventCheckSession extends AuthEvent {
  final String userId;

  const AuthEventCheckSession(this.userId);

  @override
  List<Object?> get props => [userId];
}

class AuthEventLogIn extends AuthEvent {
  final String email;
  final String password;
  final bool rememberMe;

  const AuthEventLogIn({
    required this.email,
    required this.password,
    this.rememberMe = false,
  });

  @override
  List<Object?> get props => [email, password, rememberMe];
}

class AuthEventSignUp extends AuthEvent {
  final String name;
  final String email;
  final String password;

  const AuthEventSignUp({
    required this.name,
    required this.email,
    required this.password,
  });

  @override
  List<Object?> get props => [name, email, password];
}

class AuthEventLogOut extends AuthEvent {}

class AuthEventForgotPassword extends AuthEvent {
  final String email;

  const AuthEventForgotPassword(this.email);

  @override
  List<Object?> get props => [email];
}

class AuthEventUpdateProfile extends AuthEvent {
  final User user;

  const AuthEventUpdateProfile(this.user);

  @override
  List<Object?> get props => [user];
}

abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object?> get props => [];
}

class AuthStateInitial extends AuthState {}

class AuthStateLoading extends AuthState {}

class AuthStateAuthenticated extends AuthState {
  final User user;

  const AuthStateAuthenticated(this.user);

  @override
  List<Object?> get props => [user];
}

class AuthStateUnauthenticated extends AuthState {}

class AuthStateError extends AuthState {
  final String message;

  const AuthStateError(this.message);

  @override
  List<Object?> get props => [message];
}

class AuthStatePasswordResetSent extends AuthState {}
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final AuthRepository authRepository;
  final SharedPreferences prefs;

  AuthBloc({
    required this.authRepository,
    required this.prefs,
  }) : super(AuthStateInitial()) {
    on<AuthEventInitial>(_onInitial);
    on<AuthEventCheckSession>(_onCheckSession);
    on<AuthEventLogIn>(_onLogIn);
    on<AuthEventSignUp>(_onSignUp);
    on<AuthEventLogOut>(_onLogOut);
    on<AuthEventForgotPassword>(_onForgotPassword);
    on<AuthEventUpdateProfile>(_onUpdateProfile);
  }

  Future<void> _onInitial(AuthEventInitial event, Emitter<AuthState> emit) async {
    emit(AuthStateUnauthenticated());
  }

  Future<void> _onCheckSession(AuthEventCheckSession event, Emitter<AuthState> emit) async {
    emit(AuthStateLoading());

    try {
      final user = await authRepository.getUserById(event.userId);

      if (user != null) {
        emit(AuthStateAuthenticated(user));
      } else {
        await prefs.remove('isLoggedIn');
        await prefs.remove('userId');
        emit(AuthStateUnauthenticated());
      }
    } catch (e) {
      await prefs.remove('isLoggedIn');
      await prefs.remove('userId');
      emit(AuthStateError('فشل التحقق من الجلسة: ${e.toString()}'));
    }
  }

  Future<void> _onLogIn(AuthEventLogIn event, Emitter<AuthState> emit) async {
    emit(AuthStateLoading());

    try {
      final user = await authRepository.signIn(
        email: event.email,
        password: event.password,
      );

      if (event.rememberMe) {
        await prefs.setBool('isLoggedIn', true);
        await prefs.setString('userId', user.id);
      }

      emit(AuthStateAuthenticated(user));
    } on firebase_auth.FirebaseAuthException catch (e) {
      String errorMsg = 'حدث خطأ أثناء تسجيل الدخول';

      switch (e.code) {
        case 'user-not-found':
          errorMsg = 'لم يتم العثور على حساب بهذا البريد الإلكتروني';
          break;
        case 'wrong-password':
          errorMsg = 'كلمة المرور غير صحيحة';
          break;
        case 'invalid-email':
          errorMsg = 'البريد الإلكتروني غير صالح';
          break;
        default:
          errorMsg = 'حدث خطأ: ${e.message}';
      }

      emit(AuthStateError(errorMsg));
    } catch (e) {
      emit(AuthStateError(e.toString()));
    }
  }

  Future<void> _onSignUp(AuthEventSignUp event, Emitter<AuthState> emit) async {
    emit(AuthStateLoading());

    try {
      final user = await authRepository.signUp(
        name: event.name,
        email: event.email,
        password: event.password,
      );

      await prefs.setBool('isLoggedIn', true);
      await prefs.setString('userId', user.id);

      emit(AuthStateAuthenticated(user));
    } on firebase_auth.FirebaseAuthException catch (e) {
      String errorMsg = 'حدث خطأ أثناء إنشاء الحساب';

      switch (e.code) {
        case 'email-already-in-use':
          errorMsg = 'البريد الإلكتروني مستخدم بالفعل';
          break;
        case 'weak-password':
          errorMsg = 'كلمة المرور ضعيفة جدًا';
          break;
        case 'invalid-email':
          errorMsg = 'البريد الإلكتروني غير صالح';
          break;
        default:
          errorMsg = 'حدث خطأ: ${e.message}';
      }

      emit(AuthStateError(errorMsg));
    } catch (e) {
      emit(AuthStateError(e.toString()));
    }
  }

  Future<void> _onLogOut(AuthEventLogOut event, Emitter<AuthState> emit) async {
    emit(AuthStateLoading());

    try {
      await authRepository.signOut();

      await prefs.remove('isLoggedIn');
      await prefs.remove('userId');

      emit(AuthStateUnauthenticated());
    } catch (e) {
      emit(AuthStateError('فشل تسجيل الخروج: ${e.toString()}'));
    }
  }

  Future<void> _onForgotPassword(AuthEventForgotPassword event, Emitter<AuthState> emit) async {
    emit(AuthStateLoading());

    try {
      await authRepository.resetPassword(event.email);

      emit(AuthStatePasswordResetSent());
    } catch (e) {
      emit(AuthStateError('فشل إرسال رابط إعادة تعيين كلمة المرور: ${e.toString()}'));
    }
  }

  Future<void> _onUpdateProfile(AuthEventUpdateProfile event, Emitter<AuthState> emit) async {
    emit(AuthStateLoading());

    try {
      final updatedUser = await authRepository.updateProfile(event.user);

      emit(AuthStateAuthenticated(updatedUser));
    } catch (e) {
      emit(AuthStateError('فشل تحديث الملف الشخصي: ${e.toString()}'));
    }
  }
}