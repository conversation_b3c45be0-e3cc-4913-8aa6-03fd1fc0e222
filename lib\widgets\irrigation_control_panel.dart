
import 'package:flutter/material.dart';
import 'package:sam05/models/app_models.dart';

/// لوحة التحكم في نظام الري
class IrrigationControlPanel extends StatefulWidget {
  final IrrigationSystem system;
  final Function(IrrigationType type, int duration) onStartIrrigation;
  final VoidCallback onStopIrrigation;

  const IrrigationControlPanel({
    super.key,
    required this.system,
    required this.onStartIrrigation,
    required this.onStopIrrigation,
  });

  @override
  State<IrrigationControlPanel> createState() => _IrrigationControlPanelState();
}

class _IrrigationControlPanelState extends State<IrrigationControlPanel> {
  IrrigationType _selectedType = IrrigationType.manual;
  int _duration = 30; // بالدقائق
  bool _isIrrigating = false;

  @override
  void initState() {
    super.initState();
    _isIrrigating = widget.system.currentIrrigationStatus == IrrigationStatus.running;
  }

  @override
  void didUpdateWidget(IrrigationControlPanel oldWidget) {
    super.didUpdateWidget(oldWidget);
    // تحديث حالة الري عند تغير النظام
    setState(() {
      _isIrrigating = widget.system.currentIrrigationStatus == IrrigationStatus.running;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Row(
              children: [
                const Icon(Icons.control_camera, color: Colors.green),
                const SizedBox(width: 8),
                const Text(
                  'لوحة التحكم',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                _buildSystemStatusChip(),
              ],
            ),
            const SizedBox(height: 16),

            // حالة الري الحالية
            _buildCurrentStatus(),
            const SizedBox(height: 16),

            // اختيار نوع الري
            _buildIrrigationTypeSelector(),
            const SizedBox(height: 16),

            // إعداد المدة
            _buildDurationSelector(),
            const SizedBox(height: 20),

            // أزرار التحكم
            _buildControlButtons(),
          ],
        ),
      ),
    );
  }

  /// بناء شريحة حالة النظام
  Widget _buildSystemStatusChip() {
    final status = widget.system.status;
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getStatusColor(status).withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getStatusIcon(status),
            size: 12,
            color: _getStatusColor(status),
          ),
          const SizedBox(width: 4),
          Text(
            _getStatusName(status),
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: _getStatusColor(status),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حالة الري الحالية
  Widget _buildCurrentStatus() {
    final irrigationStatus = widget.system.currentIrrigationStatus;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _getIrrigationStatusColor(irrigationStatus).withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _getIrrigationStatusColor(irrigationStatus).withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            _getIrrigationStatusIcon(irrigationStatus),
            color: _getIrrigationStatusColor(irrigationStatus),
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getIrrigationStatusTitle(irrigationStatus),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  _getIrrigationStatusDescription(irrigationStatus),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء محدد نوع الري
  Widget _buildIrrigationTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'نوع الري',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildTypeOption(
                IrrigationType.manual,
                'يدوي',
                Icons.touch_app,
                'تحكم مباشر من المستخدم',
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildTypeOption(
                IrrigationType.automatic,
                'تلقائي',
                Icons.schedule,
                'حسب العتبات المحددة',
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildTypeOption(
                IrrigationType.smart,
                'ذكي',
                Icons.psychology,
                'حسب نوع النبات',
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء خيار نوع الري
  Widget _buildTypeOption(
    IrrigationType type,
    String title,
    IconData icon,
    String tooltip,
  ) {
    final isSelected = _selectedType == type;
    final isDisabled = _isIrrigating;
    
    return Tooltip(
      message: tooltip,
      child: InkWell(
        onTap: isDisabled ? null : () => setState(() => _selectedType = type),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
          decoration: BoxDecoration(
            color: isSelected
                ? Theme.of(context).primaryColor.withOpacity(isDisabled ? 0.3 : 0.2)
                : Colors.grey.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isSelected
                  ? Theme.of(context).primaryColor.withOpacity(isDisabled ? 0.3 : 1)
                  : Colors.grey.withOpacity(0.3),
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: isSelected
                    ? Theme.of(context).primaryColor.withOpacity(isDisabled ? 0.5 : 1)
                    : Colors.grey.shade600,
                size: 20,
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  color: isSelected
                      ? Theme.of(context).primaryColor.withOpacity(isDisabled ? 0.5 : 1)
                      : Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء محدد المدة
  Widget _buildDurationSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              'مدة الري',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              '$_duration دقيقة',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: Theme.of(context).primaryColor,
            inactiveTrackColor: Colors.grey.shade300,
            thumbColor: Theme.of(context).primaryColor,
            overlayColor: Theme.of(context).primaryColor.withOpacity(0.2),
            trackHeight: 4,
            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
            overlayShape: const RoundSliderOverlayShape(overlayRadius: 16),
          ),
          child: Slider(
            value: _duration.toDouble(),
            min: 5,
            max: 60,
            divisions: 11,
            onChanged: _isIrrigating ? null : (value) {
              setState(() {
                _duration = value.round();
              });
            },
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '5 دقائق',
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey.shade600,
              ),
            ),
            Text(
              '60 دقيقة',
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء أزرار التحكم
  Widget _buildControlButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isIrrigating ? null : _startIrrigation,
            icon: const Icon(Icons.play_arrow),
            label: const Text('بدء الري'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isIrrigating ? _stopIrrigation : null,
            icon: const Icon(Icons.stop),
            label: const Text('إيقاف الري'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // ===== دوال مساعدة =====

  void _startIrrigation() {
    widget.onStartIrrigation(_selectedType, _duration);
    setState(() {
      _isIrrigating = true;
    });
  }

  void _stopIrrigation() {
    widget.onStopIrrigation();
    setState(() {
      _isIrrigating = false;
    });
  }

  Color _getStatusColor(IrrigationSystemStatus status) {
    switch (status) {
      case IrrigationSystemStatus.active:
        return Colors.green;
      case IrrigationSystemStatus.inactive:
        return Colors.grey;
      case IrrigationSystemStatus.maintenance:
        return Colors.orange;
      case IrrigationSystemStatus.error:
        return Colors.red;
    }
  }

  IconData _getStatusIcon(IrrigationSystemStatus status) {
    switch (status) {
      case IrrigationSystemStatus.active:
        return Icons.check_circle;
      case IrrigationSystemStatus.inactive:
        return Icons.pause_circle;
      case IrrigationSystemStatus.maintenance:
        return Icons.build_circle;
      case IrrigationSystemStatus.error:
        return Icons.error;
    }
  }

  String _getStatusName(IrrigationSystemStatus status) {
    switch (status) {
      case IrrigationSystemStatus.active:
        return 'نشط';
      case IrrigationSystemStatus.inactive:
        return 'غير نشط';
      case IrrigationSystemStatus.maintenance:
        return 'صيانة';
      case IrrigationSystemStatus.error:
        return 'خطأ';
    }
  }

  Color _getIrrigationStatusColor(IrrigationStatus status) {
    switch (status) {
      case IrrigationStatus.idle:
        return Colors.grey;
      case IrrigationStatus.running:
        return Colors.green;
      case IrrigationStatus.paused:
        return Colors.orange;
      case IrrigationStatus.error:
        return Colors.red;
    }
  }

  IconData _getIrrigationStatusIcon(IrrigationStatus status) {
    switch (status) {
      case IrrigationStatus.idle:
        return Icons.power_settings_new;
      case IrrigationStatus.running:
        return Icons.water;
      case IrrigationStatus.paused:
        return Icons.pause_circle;
      case IrrigationStatus.error:
        return Icons.error;
    }
  }

  String _getIrrigationStatusTitle(IrrigationStatus status) {
    switch (status) {
      case IrrigationStatus.idle:
        return 'نظام الري متوقف';
      case IrrigationStatus.running:
        return 'نظام الري يعمل';
      case IrrigationStatus.paused:
        return 'نظام الري متوقف مؤقت<|im_start|>';
      case IrrigationStatus.error:
        return 'خطأ في نظام الري';
    }
  }

  String _getIrrigationStatusDescription(IrrigationStatus status) {
    switch (status) {
      case IrrigationStatus.idle:
        return 'النظام جاهز للتشغيل';
      case IrrigationStatus.running:
        return 'جاري ري النباتات...';
      case IrrigationStatus.paused:
        return 'تم إيقاف الري مؤقت';
      case IrrigationStatus.error:
        return 'يرجى التحقق من النظام';
    }
  }
}


