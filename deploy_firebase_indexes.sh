#!/bin/bash

# 🔥 Firebase Firestore Indexes Deployment Script
# This script deploys all required Firestore indexes for the SAM05 project

echo "🚀 Starting Firebase Firestore Indexes Deployment..."
echo "=================================================="

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI is not installed."
    echo "📦 Please install it using: npm install -g firebase-tools"
    exit 1
fi

# Check if user is logged in
if ! firebase projects:list &> /dev/null; then
    echo "🔐 Please login to Firebase first:"
    echo "firebase login"
    exit 1
fi

# Check if firestore.indexes.json exists
if [ ! -f "firestore.indexes.json" ]; then
    echo "❌ firestore.indexes.json file not found!"
    echo "Please make sure you're in the project root directory."
    exit 1
fi

echo "📋 Found firestore.indexes.json file"
echo "🔍 Validating index configuration..."

# Deploy indexes
echo "🚀 Deploying Firestore indexes..."
firebase deploy --only firestore:indexes

if [ $? -eq 0 ]; then
    echo "✅ Firestore indexes deployed successfully!"
    echo ""
    echo "📊 Index Status:"
    echo "- irrigation_systems indexes: ✅ Deployed"
    echo "- water_usage_stats indexes: ✅ Deployed"
    echo "- irrigation_records indexes: ✅ Deployed"
    echo "- sensor_readings indexes: ✅ Deployed"
    echo "- products indexes: ✅ Deployed"
    echo "- irrigation_logs indexes: ✅ Deployed"
    echo "- messages indexes: ✅ Deployed"
    echo "- chats indexes: ✅ Deployed"
    echo ""
    echo "⏱️  Note: Index creation may take a few minutes to complete."
    echo "🔗 Monitor progress at: https://console.firebase.google.com/project/sam03-b6433/firestore/indexes"
else
    echo "❌ Failed to deploy indexes!"
    echo "🔧 Troubleshooting steps:"
    echo "1. Check your internet connection"
    echo "2. Verify Firebase project permissions"
    echo "3. Ensure you're in the correct project directory"
    echo "4. Try: firebase use sam03-b6433"
    exit 1
fi

echo ""
echo "🎉 Deployment completed!"
echo "📱 You can now restart your Flutter app:"
echo "flutter hot restart"
