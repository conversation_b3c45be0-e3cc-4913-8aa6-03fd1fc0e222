import 'package:flutter/material.dart';
import 'package:sam05/models/app_models.dart';
import 'package:sam05/core/utils/irrigation_utils.dart';

/// Widget لاختيار نظام الري
class SystemSelectorWidget extends StatelessWidget {
  final List<IrrigationSystem> systems;
  final String? selectedSystemId;
  final Function(String) onSystemSelected;
  final VoidCallback onAddSystem;

  const SystemSelectorWidget({
    super.key,
    required this.systems,
    required this.selectedSystemId,
    required this.onSystemSelected,
    required this.onAddSystem,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 180,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان وزر الإضافة
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'أنظمة الري (${systems.length})',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              Row(
                children: [
                  // مؤشر النظام النشط
                  if (selectedSystemId != null)
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color:
                              Theme.of(context).primaryColor.withOpacity(0.3),
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.check_circle,
                            size: 14,
                            color: Theme.of(context).primaryColor,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'نشط',
                            style: TextStyle(
                              color: Theme.of(context).primaryColor,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  const SizedBox(width: 8),
                  // زر الإضافة
                  IconButton(
                    onPressed: onAddSystem,
                    icon: const Icon(Icons.add_circle_outline),
                    tooltip: 'إضافة نظام جديد',
                    color: Theme.of(context).primaryColor,
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 12),

          // قائمة الأنظمة
          Expanded(
            child: systems.isEmpty
                ? _buildEmptyState(context)
                : ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: systems.length,
                    itemBuilder: (context, index) {
                      final system = systems[index];
                      return _buildSystemCard(context, system);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة النظام
  Widget _buildSystemCard(BuildContext context, IrrigationSystem system) {
    final isSelected = system.id == selectedSystemId;

    return GestureDetector(
      onTap: () => onSystemSelected(system.id),
      child: Container(
        width: 240,
        margin: const EdgeInsets.only(right: 12),
        decoration: BoxDecoration(
          color: isSelected ? Theme.of(context).primaryColor : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected
                ? Theme.of(context).primaryColor
                : Colors.grey.shade300,
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(10), // تقليل padding من 12 إلى 10
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min, // إضافة هذا لتقليل حجم العمود
            children: [
              // الصف الأول: الأيقونة والاسم ومؤشر الاتصال
              Row(
                children: [
                  Icon(
                    IrrigationUtils.getSystemTypeIcon(system.type),
                    color: isSelected
                        ? Colors.white
                        : Theme.of(context).primaryColor,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      system.name,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: isSelected ? Colors.white : Colors.black87,
                        fontSize: 14,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1, // تحديد عدد الأسطر
                    ),
                  ),
                  // مؤشر الاتصال
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: system.isOnline ? Colors.green : Colors.red,
                      shape: BoxShape.circle,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4), // تقليل المسافة من 6 إلى 4

              // نوع النظام
              Text(
                IrrigationUtils.getSystemTypeName(system.type),
                style: TextStyle(
                  color: isSelected ? Colors.white70 : Colors.grey.shade600,
                  fontSize: 12,
                ),
                maxLines: 1, // تحديد عدد الأسطر
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 2), // تقليل المسافة من 4 إلى 2

              // الموقع
              Text(
                system.location ?? 'غير محدد',
                style: TextStyle(
                  color: isSelected ? Colors.white60 : Colors.grey.shade500,
                  fontSize: 11,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1, // تحديد عدد الأسطر
              ),
              const SizedBox(height: 4), // تقليل المسافة من 6 إلى 4

              // حالة النظام
              Row(
                children: [
                  Icon(
                    IrrigationUtils.getSystemStatusIcon(
                        system.status.toString().split('.').last),
                    size: 14,
                    color: isSelected
                        ? Colors.white70
                        : IrrigationUtils.getSystemStatusColor(
                            system.status.toString().split('.').last),
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      IrrigationUtils.getSystemStatusName(
                          system.status.toString().split('.').last),
                      style: TextStyle(
                        color: isSelected
                            ? Colors.white70
                            : IrrigationUtils.getSystemStatusColor(
                                system.status.toString().split('.').last),
                        fontSize: 11,
                        fontWeight: FontWeight.w500,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1, // تحديد عدد الأسطر
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.water_drop_outlined,
            size: 32,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 8),
          Text(
            'لا توجد أنظمة ري',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'اضغط على + لإضافة نظام جديد',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }
}
