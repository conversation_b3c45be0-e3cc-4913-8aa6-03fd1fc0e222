import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sam05/bloc/irrigation_bloc.dart';
import 'package:sam05/models/app_models.dart';
import 'package:sam05/widgets/sensor_gauge_widget.dart';
import 'package:sam05/widgets/irrigation_control_panel.dart';
import 'package:sam05/widgets/irrigation_logs_widget.dart';
import 'package:sam05/widgets/system_selector_widget.dart';
import 'package:sam05/widgets/system_info_widget.dart';

/// شاشة نظام الري الذكي المحدثة
class SmartIrrigationScreen extends StatefulWidget {
  const SmartIrrigationScreen({super.key});

  @override
  State<SmartIrrigationScreen> createState() => _SmartIrrigationScreenV2State();
}

class _SmartIrrigationScreenV2State extends State<SmartIrrigationScreen> {
  String? _selectedSystemId;
  bool _isMonitoring = false;
  bool _isRealtimeMonitoring = false;

  @override
  void initState() {
    super.initState();
    // تحميل أنظمة الري عند بدء الشاشة
    context.read<IrrigationBloc>().add(
          const IrrigationEventLoadSystems(
              null), // سيتم استخدام معرف المستخدم الحقيقي من UserService
        );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('نظام الري الذكي'),
        actions: [
          IconButton(
            icon:
                Icon(_isRealtimeMonitoring ? Icons.sensors : Icons.sensors_off),
            onPressed: _toggleRealtimeMonitoring,
            tooltip: _isRealtimeMonitoring
                ? 'إيقاف المراقبة المباشرة'
                : 'بدء المراقبة المباشرة',
          ),
          IconButton(
            icon: Icon(_isMonitoring ? Icons.pause : Icons.play_arrow),
            onPressed: _toggleMonitoring,
            tooltip: _isMonitoring ? 'إيقاف المراقبة' : 'بدء المراقبة',
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showAddSystemDialog,
            tooltip: 'إضافة نظام جديد',
          ),
        ],
      ),
      body: BlocConsumer<IrrigationBloc, IrrigationState>(
        listener: (context, state) {
          if (state is IrrigationStateError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          } else if (state is IrrigationStateIrrigationStarted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم بدء الري للنظام ${state.systemId}'),
                backgroundColor: Colors.green,
              ),
            );
          } else if (state is IrrigationStateIrrigationStopped) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم إيقاف الري للنظام ${state.systemId}'),
                backgroundColor: Colors.orange,
              ),
            );
          } else if (state is IrrigationStateMonitoringStarted) {
            setState(() {
              _isMonitoring = true;
            });
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم بدء المراقبة التلقائية'),
                backgroundColor: Colors.blue,
              ),
            );
          } else if (state is IrrigationStateMonitoringStopped) {
            setState(() {
              _isMonitoring = false;
            });
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم إيقاف المراقبة التلقائية'),
                backgroundColor: Colors.grey,
              ),
            );
          } else if (state is IrrigationStateRealtimeMonitoringStarted) {
            setState(() {
              _isRealtimeMonitoring = true;
            });
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم بدء المراقبة المباشرة للحساسات'),
                backgroundColor: Colors.blue,
              ),
            );
          } else if (state is IrrigationStateRealtimeMonitoringStopped) {
            setState(() {
              _isRealtimeMonitoring = false;
            });
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم إيقاف المراقبة المباشرة للحساسات'),
                backgroundColor: Colors.orange,
              ),
            );
          } else if (state is IrrigationStateRealtimeSensorUpdate) {
            // تحديث مباشر للحساسات - يمكن إضافة تأثيرات بصرية هنا
            debugPrint('📊 تحديث مباشر للحساسات: ${state.systemId}');
          }
        },
        builder: (context, state) {
          if (state is IrrigationStateLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is IrrigationStateSystemsLoaded) {
            if (state.systems.isEmpty) {
              return _buildEmptyState();
            }

            // تحديد النظام المحدد
            if (_selectedSystemId == null && state.systems.isNotEmpty) {
              _selectedSystemId = state.systems.first.id;
              WidgetsBinding.instance.addPostFrameCallback((_) {
                context.read<IrrigationBloc>().add(
                      IrrigationEventSelectSystem(_selectedSystemId!),
                    );
              });
            }

            return _buildSystemsView(state.systems);
          }

          if (state is IrrigationStateSystemSelected) {
            return _buildSystemDetailView(state.system, state.settings);
          }

          return _buildEmptyState();
        },
      ),
    );
  }

  /// بناء عرض الأنظمة
  Widget _buildSystemsView(List<IrrigationSystem> systems) {
    return Column(
      children: [
        // شريط اختيار النظام
        SystemSelectorWidget(
          systems: systems,
          selectedSystemId: _selectedSystemId,
          onSystemSelected: _selectSystem,
          onAddSystem: _showAddSystemDialog,
        ),

        // عرض النظام المحدد
        Expanded(
          child: _selectedSystemId != null
              ? _buildSystemDetails(
                  systems.firstWhere((s) => s.id == _selectedSystemId))
              : _buildEmptyState(),
        ),
      ],
    );
  }

  /// بناء تفاصيل النظام المحدد
  Widget _buildSystemDetails(IrrigationSystem system) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // معلومات النظام
          BlocBuilder<IrrigationBloc, IrrigationState>(
            builder: (context, state) {
              IrrigationSettings? settings;
              if (state is IrrigationStateSystemSelected) {
                settings = state.settings;
              }
              return SystemInfoWidget(
                system: system,
                settings: settings,
                onEditSystem: () => _showEditSystemDialog(system),
                onDeleteSystem: () => _deleteSystem(system.id),
              );
            },
          ),
          const SizedBox(height: 16),

          // بيانات الحساسات
          _buildSensorsSection(system),
          const SizedBox(height: 24),

          // لوحة التحكم
          _buildControlSection(system),
          const SizedBox(height: 24),

          // سجل الري
          _buildLogsSection(system),
        ],
      ),
    );
  }

  /// بناء قسم الحساسات
  Widget _buildSensorsSection(IrrigationSystem system) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.sensors, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  'قراءات الحساسات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 8),
                if (_isRealtimeMonitoring)
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.circle, color: Colors.white, size: 8),
                        SizedBox(width: 4),
                        Text(
                          'مباشر',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                const Spacer(),
                Text(
                  'آخر تحديث: ${_formatTime(system.lastUpdate)}',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // مقاييس الحساسات
            SizedBox(
              height: 200,
              child: Row(
                children: [
                  Expanded(
                    child: SensorGaugeWidget(
                      title: 'رطوبة التربة',
                      value: system.sensors.soilMoisture,
                      unit: '%',
                      color: Colors.brown,
                      icon: Icons.water_drop,
                    ),
                  ),
                  Expanded(
                    child: SensorGaugeWidget(
                      title: 'درجة الحرارة',
                      value: system.sensors.temperature / 50, // تحويل إلى نسبة
                      unit: '°C',
                      color: Colors.orange,
                      icon: Icons.thermostat,
                      displayValue:
                          system.sensors.temperature.toStringAsFixed(1),
                    ),
                  ),
                  Expanded(
                    child: SensorGaugeWidget(
                      title: 'الرطوبة الجوية',
                      value: system.sensors.humidity,
                      unit: '%',
                      color: Colors.blue,
                      icon: Icons.cloud,
                    ),
                  ),
                  Expanded(
                    child: SensorGaugeWidget(
                      title: 'مستوى المياه',
                      value: system.sensors.waterLevel,
                      unit: '%',
                      color: Colors.cyan,
                      icon: Icons.water,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم التحكم
  Widget _buildControlSection(IrrigationSystem system) {
    return IrrigationControlPanel(
      system: system,
      onStartIrrigation: (type, duration) =>
          _startIrrigation(system.id, type, duration),
      onStopIrrigation: () => _stopIrrigation(system.id),
    );
  }

  /// بناء قسم السجل
  Widget _buildLogsSection(IrrigationSystem system) {
    return IrrigationLogsWidget(systemId: system.id);
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.water_drop_outlined,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد أنظمة ري',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اضغط على + لإضافة نظام ري جديد',
            style: TextStyle(
              color: Colors.grey.shade500,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _showAddSystemDialog,
            icon: const Icon(Icons.add),
            label: const Text('إضافة نظام ري'),
          ),
        ],
      ),
    );
  }

  // ===== دوال مساعدة =====

  void _selectSystem(String systemId) {
    setState(() {
      _selectedSystemId = systemId;
    });
    context.read<IrrigationBloc>().add(IrrigationEventSelectSystem(systemId));
  }

  void _toggleMonitoring() {
    if (_isMonitoring) {
      context.read<IrrigationBloc>().add(const IrrigationEventStopMonitoring());
    } else {
      context
          .read<IrrigationBloc>()
          .add(const IrrigationEventStartMonitoring('current_user'));
    }
  }

  void _startIrrigation(String systemId, IrrigationType type, int duration) {
    context.read<IrrigationBloc>().add(
          IrrigationEventStartIrrigationWithType(
            systemId: systemId,
            type: type,
            duration: duration,
          ),
        );
  }

  void _stopIrrigation(String systemId) {
    context.read<IrrigationBloc>().add(IrrigationEventStopIrrigation(systemId));
  }

  void _showAddSystemDialog() {
    _showSystemDialog();
  }

  void _showEditSystemDialog(IrrigationSystem system) {
    _showSystemDialog(system: system);
  }

  void _showSystemDialog({IrrigationSystem? system}) {
    final formKey = GlobalKey<FormState>();
    String name = system?.name ?? '';
    String type = system?.type ?? 'drip';
    String serialNumber = system?.serialNumber ?? '';
    String? description = system?.description;
    String? location = system?.location;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(system == null ? 'إضافة نظام ري جديد' : 'تعديل نظام الري'),
        content: Form(
          key: formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // اسم النظام
                TextFormField(
                  initialValue: name,
                  decoration: const InputDecoration(
                    labelText: 'اسم النظام',
                    hintText: 'مثال: نظام ري الحديقة',
                    prefixIcon: Icon(Icons.title),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال اسم النظام';
                    }
                    return null;
                  },
                  onSaved: (value) => name = value!,
                ),
                const SizedBox(height: 16),

                // نوع النظام
                DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'نوع النظام',
                    prefixIcon: Icon(Icons.category),
                  ),
                  value: type,
                  items: const [
                    DropdownMenuItem(
                      value: 'drip',
                      child: Text('ري بالتنقيط'),
                    ),
                    DropdownMenuItem(
                      value: 'sprinkler',
                      child: Text('ري بالرش'),
                    ),
                    DropdownMenuItem(
                      value: 'micro',
                      child: Text('ري دقيق'),
                    ),
                  ],
                  onChanged: (value) => type = value!,
                ),
                const SizedBox(height: 16),

                // الرقم التسلسلي
                TextFormField(
                  initialValue: serialNumber,
                  decoration: const InputDecoration(
                    labelText: 'الرقم التسلسلي',
                    hintText: 'مثال: IR-12345',
                    prefixIcon: Icon(Icons.qr_code),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال الرقم التسلسلي';
                    }
                    return null;
                  },
                  onSaved: (value) => serialNumber = value!,
                ),
                const SizedBox(height: 16),

                // الوصف
                TextFormField(
                  initialValue: description,
                  decoration: const InputDecoration(
                    labelText: 'الوصف (اختياري)',
                    hintText: 'وصف مختصر للنظام',
                    prefixIcon: Icon(Icons.description),
                  ),
                  onSaved: (value) => description = value,
                ),
                const SizedBox(height: 16),

                // الموقع
                TextFormField(
                  initialValue: location,
                  decoration: const InputDecoration(
                    labelText: 'الموقع (اختياري)',
                    hintText: 'مثال: الحديقة الأمامية',
                    prefixIcon: Icon(Icons.location_on),
                  ),
                  onSaved: (value) => location = value,
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (formKey.currentState!.validate()) {
                formKey.currentState!.save();

                if (system == null) {
                  // إضافة النظام الجديد
                  context.read<IrrigationBloc>().add(
                        IrrigationEventAddSystem(
                          name: name,
                          type: type,
                          serialNumber: serialNumber,
                          description: description,
                          location: location,
                        ),
                      );
                } else {
                  // تحديث النظام الموجود
                  final updatedSystem = system.copyWith(
                    name: name,
                    type: type,
                    serialNumber: serialNumber,
                    description: description,
                    location: location,
                  );

                  // TODO: إضافة حدث تحديث النظام في BLoC
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('سيتم إضافة ميزة التحديث قريباً'),
                      backgroundColor: Colors.orange,
                    ),
                  );
                }

                Navigator.pop(context);
              }
            },
            child: Text(system == null ? 'إضافة' : 'تحديث'),
          ),
        ],
      ),
    );
  }

  void _deleteSystem(String systemId) {
    // TODO: إضافة حدث حذف النظام في BLoC
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم إضافة ميزة الحذف قريباً'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// بناء تفاصيل النظام المحدد
  Widget _buildSystemDetailView(
      IrrigationSystem system, IrrigationSettings? settings) {
    return _buildSystemDetails(system);
  }

  /// تبديل حالة المراقبة المباشرة
  void _toggleRealtimeMonitoring() {
    if (_selectedSystemId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى تحديد نظام ري أولاً'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    final bloc = context.read<IrrigationBloc>();
    if (_isRealtimeMonitoring) {
      bloc.stopRealtimeMonitoring(_selectedSystemId!);
    } else {
      bloc.startRealtimeMonitoring(_selectedSystemId!);
    }
  }

  @override
  void dispose() {
    // إيقاف جميع مراقبات Real-time عند إغلاق الشاشة
    context.read<IrrigationBloc>().stopAllRealtimeMonitoring();
    super.dispose();
  }
}
