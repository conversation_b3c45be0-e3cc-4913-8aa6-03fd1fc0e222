import 'package:flutter/material.dart';
import 'package:sam05/bloc/auth_bloc.dart';
import 'package:sam05/bloc/irrigation_bloc.dart';
import 'package:sam05/bloc/market_bloc.dart';
import 'package:sam05/bloc/theme_bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:firebase_core/firebase_core.dart';

import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import 'firebase_options.dart';
import 'repositories/auth_repository.dart';
import 'repositories/market_repository.dart';
import 'repositories/irrigation_repository.dart';
import 'services/database_service.dart';
import 'services/database_fix_service.dart';
import 'services/user_service.dart';
import 'screens/auth_screens.dart';
import 'screens/home_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // تهيئة قاعدة البيانات
  try {
    await DatabaseService().initializeDatabase();

    // تطبيق الإصلاح الشامل لقاعدة البيانات
    final missingIndexUrls =
        await DatabaseFixService().performComprehensiveFix();

    if (missingIndexUrls.isEmpty) {
      debugPrint('✅ تم تهيئة قاعدة البيانات بنجاح - جميع الفهارس متوفرة');
    } else {
      debugPrint(
          '⚠️ تم تهيئة قاعدة البيانات مع وجود ${missingIndexUrls.length} فهرس مفقود');
      debugPrint('📋 روابط إنشاء الفهارس المطلوبة:');
      debugPrint('=' * 80);

      for (int i = 0; i < missingIndexUrls.length; i++) {
        debugPrint('🔗 فهرس ${i + 1}:');
        debugPrint('   ${missingIndexUrls[i]}');
        debugPrint('');
      }

      debugPrint('=' * 80);
      debugPrint('📝 تعليمات:');
      debugPrint('   1. انسخ كل رابط واحد تلو الآخر');
      debugPrint('   2. افتح الرابط في المتصفح');
      debugPrint('   3. اضغط "Create Index" في Firebase Console');
      debugPrint('   4. انتظر حتى يكتمل إنشاء الفهرس');
      debugPrint('   5. أعد تشغيل التطبيق');
      debugPrint('=' * 80);
    }
  } catch (e) {
    debugPrint('تحذير: فشل في تهيئة قاعدة البيانات: $e');

    // محاولة الإصلاح السريع في حالة الفشل
    try {
      await DatabaseFixService().quickFix();
      debugPrint('✅ تم تطبيق الإصلاح السريع');
    } catch (fixError) {
      debugPrint('❌ فشل في الإصلاح السريع: $fixError');
    }
  }

  final prefs = await SharedPreferences.getInstance();
  final bool isLoggedIn = prefs.getBool('isLoggedIn') ?? false;
  final String? userId = prefs.getString('userId');
  final bool isDarkMode = prefs.getBool('isDarkMode') ?? false;

  runApp(
    MultiBlocProvider(
      providers: [
        BlocProvider<AuthBloc>(
          create: (context) => AuthBloc(
            authRepository: AuthRepository(),
            prefs: prefs,
          )..add(isLoggedIn && userId != null
              ? AuthEventCheckSession(userId)
              : AuthEventInitial()),
        ),
        BlocProvider<ThemeBloc>(
          create: (context) => ThemeBloc(
            prefs: prefs,
            initialIsDark: isDarkMode,
          ),
        ),
        BlocProvider<MarketBloc>(
          create: (context) => MarketBloc(marketRepository: MarketRepository())
            ..add(MarketEventLoadProducts()),
        ),
        BlocProvider<IrrigationBloc>(
          create: (context) => IrrigationBloc(
            irrigationRepository: IrrigationRepository(),
          ),
        ),
      ],
      child: const AgriculturalMarketApp(),
    ),
  );
}

class AgriculturalMarketApp extends StatelessWidget {
  const AgriculturalMarketApp({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return MaterialApp(
          title: 'SAM - مساعدك الزراعي الذكي',
          debugShowCheckedModeBanner: false,
          theme: ThemeData(
            primaryColor: const Color(0xFF2E7D32),
            colorScheme: ColorScheme.light(
              primary: const Color(0xFF2E7D32),
              primaryContainer: const Color(0xFF4CAF50),
              secondary: const Color(0xFF795548),
              surface: Colors.white,
              error: const Color(0xFFD32F2F),
              onPrimary: Colors.white,
              onSecondary: Colors.white,
              onSurface: const Color(0xFF212121),
              onError: Colors.white,
            ),
            fontFamily: 'Tajawal',
            appBarTheme: const AppBarTheme(
              backgroundColor: Colors.white,
              foregroundColor: Color(0xFF212121),
              elevation: 1,
              centerTitle: true,
            ),
            cardTheme: CardTheme(
              elevation: 2,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
            ),
          ),
          darkTheme: ThemeData(
            primaryColor: const Color(0xFF4CAF50),
            colorScheme: ColorScheme.dark(
              primary: const Color(0xFF4CAF50),
              primaryContainer: const Color(0xFF2E7D32),
              secondary: const Color(0xFF795548),
              surface: const Color(0xFF1E1E1E),
              error: const Color(0xFFD32F2F),
              onPrimary: Colors.white,
              onSecondary: Colors.white,
              onSurface: const Color(0xFFFAFAFA),
              onError: Colors.white,
            ),
            fontFamily: 'Tajawal',
            appBarTheme: const AppBarTheme(
              backgroundColor: Color(0xFF1E1E1E),
              foregroundColor: Color(0xFFFAFAFA),
              elevation: 1,
              centerTitle: true,
            ),
            cardTheme: CardTheme(
              color: const Color(0xFF1E1E1E),
              elevation: 2,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
            ),
          ),
          themeMode: themeState.isDark ? ThemeMode.dark : ThemeMode.light,
          locale: const Locale('ar'),
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('ar', ''),
          ],
          home: BlocBuilder<AuthBloc, AuthState>(
            builder: (context, state) {
              if (state is AuthStateAuthenticated) {
                return const HomePage();
              } else {
                return const LoginScreen();
              }
            },
          ),
        );
      },
    );
  }
}
