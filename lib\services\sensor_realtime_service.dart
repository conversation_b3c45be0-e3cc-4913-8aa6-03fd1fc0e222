import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:sam05/models/app_models.dart';
import 'package:sam05/core/constants/app_constants.dart';

/// خدمة Real-time لقراءات الحساسات
/// تدير قراءة البيانات المباشرة من الأجهزة الفعلية كل 5 ثوانٍ
class SensorRealtimeService {
  static final SensorRealtimeService _instance =
      SensorRealtimeService._internal();
  factory SensorRealtimeService() => _instance;
  SensorRealtimeService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final Map<String, StreamController<SensorData>> _sensorControllers = {};
  final Map<String, StreamSubscription> _firestoreSubscriptions = {};

  /// بدء مراقبة الحساسات لنظام معين
  /// يقرأ البيانات الحقيقية من قاعدة البيانات التي يتم تحديثها من الأجهزة
  Stream<SensorData> startSensorMonitoring(String systemId) {
    // إذا كان هناك مراقبة موجودة، أوقفها أولاً
    stopSensorMonitoring(systemId);

    // إنشاء StreamController جديد
    final controller = StreamController<SensorData>.broadcast();
    _sensorControllers[systemId] = controller;

    // مراقبة التغييرات في قاعدة البيانات
    final subscription = _firestore
        .collection(AppConstants.collectionIrrigationSystems)
        .doc(systemId)
        .snapshots()
        .listen((snapshot) {
      if (snapshot.exists && snapshot.data()?['sensors'] != null) {
        try {
          final sensorData = SensorData.fromMap(
              Map<String, dynamic>.from(snapshot.data()!['sensors']));
          controller.add(sensorData);
        } catch (e) {
          print('❌ خطأ في قراءة بيانات الحساسات: $e');
          // إرسال بيانات افتراضية في حالة الخطأ
          controller.add(_getDefaultSensorData());
        }
      } else {
        // إرسال بيانات افتراضية إذا لم توجد بيانات
        controller.add(_getDefaultSensorData());
      }
    });

    _firestoreSubscriptions[systemId] = subscription;

    return controller.stream;
  }

  /// إيقاف مراقبة الحساسات لنظام معين
  void stopSensorMonitoring(String systemId) {
    _firestoreSubscriptions[systemId]?.cancel();
    _firestoreSubscriptions.remove(systemId);

    _sensorControllers[systemId]?.close();
    _sensorControllers.remove(systemId);
  }

  /// إيقاف جميع مراقبات الحساسات
  void stopAllSensorMonitoring() {
    for (final subscription in _firestoreSubscriptions.values) {
      subscription.cancel();
    }
    _firestoreSubscriptions.clear();

    for (final controller in _sensorControllers.values) {
      controller.close();
    }
    _sensorControllers.clear();
  }

  /// الحصول على بيانات افتراضية للحساسات
  /// تُستخدم عندما لا تتوفر بيانات حقيقية من الأجهزة
  SensorData _getDefaultSensorData() {
    return const SensorData(
      soilMoisture: 0.0, // لا توجد قراءة
      temperature: 0.0, // لا توجد قراءة
      humidity: 0.0, // لا توجد قراءة
      waterLevel: 0.0, // لا توجد قراءة
      batteryLevel: 0.0, // لا توجد قراءة
      rainStatus: false, // افتراضي: لا يوجد مطر
    );
  }

  /// الحصول على آخر قراءات الحساسات من Firestore
  Future<SensorData?> getLatestSensorData(String systemId) async {
    try {
      final systemDoc = await _firestore
          .collection(AppConstants.collectionIrrigationSystems)
          .doc(systemId)
          .get();

      if (systemDoc.exists && systemDoc.data()?['sensors'] != null) {
        return SensorData.fromMap(
            Map<String, dynamic>.from(systemDoc.data()!['sensors']));
      }

      return null;
    } catch (e) {
      print('❌ خطأ في جلب بيانات الحساسات: $e');
      return null;
    }
  }

  /// الحصول على السجل التاريخي للحساسات
  Stream<List<SensorReading>> getSensorHistory(String systemId,
      {int limit = 50}) {
    return _firestore
        .collection(AppConstants.collectionSensorReadings)
        .where('systemId', isEqualTo: systemId)
        .orderBy('timestamp', descending: true)
        .limit(limit)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => SensorReading.fromFirestore(doc))
            .toList());
  }

  /// فحص حالة الاتصال بالحساسات
  Future<bool> checkSensorConnectivity(String systemId) async {
    try {
      final latestData = await getLatestSensorData(systemId);
      if (latestData == null) return false;

      // فحص آخر تحديث (يجب أن يكون خلال آخر دقيقتين)
      final systemDoc = await _firestore
          .collection(AppConstants.collectionIrrigationSystems)
          .doc(systemId)
          .get();

      if (systemDoc.exists) {
        final lastUpdate =
            (systemDoc.data()?['lastUpdate'] as Timestamp?)?.toDate();
        if (lastUpdate != null) {
          final timeDiff = DateTime.now().difference(lastUpdate);
          return timeDiff.inMinutes < 2;
        }
      }

      return false;
    } catch (e) {
      print('❌ خطأ في فحص اتصال الحساسات: $e');
      return false;
    }
  }
}
