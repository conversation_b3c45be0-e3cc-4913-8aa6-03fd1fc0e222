import 'package:firebase_core/firebase_core.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../repositories/market_repository.dart';
import '../models/app_models.dart';

abstract class MarketEvent extends Equatable {
  const MarketEvent();

  @override
  List<Object?> get props => [];
}

class MarketEventLoadProducts extends MarketEvent {}

class MarketEventLoadProductDetails extends MarketEvent {
  final String productId;

  const MarketEventLoadProductDetails(this.productId);

  @override
  List<Object?> get props => [productId];
}

class MarketEventAddProduct extends MarketEvent {
  final String name;
  final String description;
  final double price;
  final String unit;
  final double quantity;
  final String category;
  final List<TempFileData> images;
  final String location;
  final String userId;
  final String userName;
  final String? userPhone;

  const MarketEventAddProduct({
    required this.name,
    required this.description,
    required this.price,
    required this.unit,
    required this.quantity,
    required this.category,
    required this.images,
    required this.location,
    required this.userId,
    required this.userName,
    this.userPhone,
  });

  @override
  List<Object?> get props => [
        name,
        description,
        price,
        unit,
        quantity,
        category,
        images,
        location,
        userId,
        userName,
        userPhone
      ];
}

class MarketEventLoadUserProducts extends MarketEvent {
  final String userId;

  const MarketEventLoadUserProducts(this.userId);

  @override
  List<Object?> get props => [userId];
}

class MarketEventUpdateProduct extends MarketEvent {
  final String id;
  final String name;
  final String description;
  final double price;
  final String unit;
  final double quantity;
  final String category;
  final List<String> imageUrls;
  final String location;
  final String? sellerPhone;

  const MarketEventUpdateProduct({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.unit,
    required this.quantity,
    required this.category,
    required this.imageUrls,
    required this.location,
    this.sellerPhone,
  });

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        price,
        unit,
        quantity,
        category,
        imageUrls,
        location,
        sellerPhone
      ];
}

class MarketEventDeleteProduct extends MarketEvent {
  final String id;

  const MarketEventDeleteProduct(this.id);

  @override
  List<Object?> get props => [id];
}

class MarketEventLoadChats extends MarketEvent {
  final String userId;

  const MarketEventLoadChats(this.userId);

  @override
  List<Object?> get props => [userId];
}

class MarketEventLoadMessages extends MarketEvent {
  final String chatId;

  const MarketEventLoadMessages(this.chatId);

  @override
  List<Object?> get props => [chatId];
}

class MarketEventSendMessage extends MarketEvent {
  final String chatId;
  final String content;
  final String senderId;
  final List<String> participants;
  final List<TempFileData>? attachments;
  final String? attachmentType;

  const MarketEventSendMessage({
    required this.chatId,
    required this.content,
    required this.senderId,
    required this.participants,
    this.attachments,
    this.attachmentType,
  });

  @override
  List<Object?> get props =>
      [chatId, content, senderId, participants, attachments, attachmentType];
}

//-----------------------------------الحالات----------------
abstract class MarketState extends Equatable {
  const MarketState();

  @override
  List<Object?> get props => [];
}

class MarketStateInitial extends MarketState {}

class MarketStateLoading extends MarketState {}

class MarketStateProductsLoaded extends MarketState {
  final List<Product> products;

  const MarketStateProductsLoaded(this.products);

  @override
  List<Object?> get props => [products];
}

class MarketStateProductDetailsLoaded extends MarketState {
  final Product product;

  const MarketStateProductDetailsLoaded(this.product);

  @override
  List<Object?> get props => [product];
}

class MarketStateProductAdded extends MarketState {
  final Product product;

  const MarketStateProductAdded(this.product);

  @override
  List<Object?> get props => [product];
}

class MarketStateUserProductsLoaded extends MarketState {
  final List<Product> products;

  const MarketStateUserProductsLoaded(this.products);

  @override
  List<Object?> get props => [products];
}

class MarketStateProductUpdated extends MarketState {
  final Product product;

  const MarketStateProductUpdated(this.product);

  @override
  List<Object?> get props => [product];
}

class MarketStateProductDeleted extends MarketState {
  final String id;

  const MarketStateProductDeleted(this.id);

  @override
  List<Object?> get props => [id];
}

class MarketStateChatsLoaded extends MarketState {
  final List<Chat> chats;

  const MarketStateChatsLoaded(this.chats);

  @override
  List<Object?> get props => [chats];
}

class MarketStateMessagesLoaded extends MarketState {
  final List<Message> messages;
  final String chatId;

  const MarketStateMessagesLoaded({
    required this.messages,
    required this.chatId,
  });

  @override
  List<Object?> get props => [messages, chatId];
}

class MarketStateMessageSent extends MarketState {
  final Message message;

  const MarketStateMessageSent(this.message);

  @override
  List<Object?> get props => [message];
}

class MarketStateError extends MarketState {
  final String message;

  const MarketStateError(this.message);

  @override
  List<Object?> get props => [message];
}

//---------------القلب النابض حيث كل جدث يتم توجيهه لدالة مناسبة من الريبو----------------------
class MarketBloc extends Bloc<MarketEvent, MarketState> {
  final MarketRepository marketRepository;

  MarketBloc({
    required this.marketRepository,
  }) : super(MarketStateInitial()) {
    on<MarketEventLoadProducts>(_onLoadProducts);
    on<MarketEventLoadProductDetails>(_onLoadProductDetails);
    on<MarketEventAddProduct>(_onAddProduct);
    on<MarketEventLoadUserProducts>(_onLoadUserProducts);
    on<MarketEventUpdateProduct>(_onUpdateProduct);
    on<MarketEventDeleteProduct>(_onDeleteProduct);
    on<MarketEventLoadChats>(_onLoadChats);
    on<MarketEventLoadMessages>(_onLoadMessages);
    on<MarketEventSendMessage>(_onSendMessage);
  }

  Future<void> _onLoadProducts(
      MarketEventLoadProducts event, Emitter<MarketState> emit) async {
    emit(MarketStateLoading());
    try {
      final products = await marketRepository.getProducts();
      emit(MarketStateProductsLoaded(products));
    } catch (e) {
      emit(MarketStateError('فشل في تحميل المنتجات: ${e.toString()}'));
    }
  }

  Future<void> _onLoadProductDetails(
      MarketEventLoadProductDetails event, Emitter<MarketState> emit) async {
    emit(MarketStateLoading());
    try {
      final product = await marketRepository.getProductById(event.productId);
      if (product != null) {
        emit(MarketStateProductDetailsLoaded(product));
      } else {
        emit(const MarketStateError('لم يتم العثور على المنتج'));
      }
    } catch (e) {
      emit(MarketStateError('فشل في تحميل تفاصيل المنتج: ${e.toString()}'));
    }
  }

  Future<void> _onAddProduct(
      MarketEventAddProduct event, Emitter<MarketState> emit) async {
    emit(MarketStateLoading());
    try {
      List<String> imageUrls = [];

      if (event.images.isNotEmpty) {
        try {
          for (var image in event.images) {
            final urls = await marketRepository.uploadProductImages([image]);
            if (urls.isNotEmpty) {
              imageUrls.addAll(urls);
            }
          }
        } catch (e) {
          // تسجيل الخطأ بدلاً من print في الإنتاج
          debugPrint('Failed to upload images: $e');
        }
      }

      // لا نضيف صورة افتراضية من Firebase لأنها غير موجودة
      // سيتم التعامل مع المنتجات بدون صور في واجهة المستخدم

      final product = await marketRepository.addProduct(
        name: event.name,
        description: event.description,
        price: event.price,
        unit: event.unit,
        quantity: event.quantity,
        category: event.category,
        imageUrls: imageUrls,
        location: event.location,
        sellerId: event.userId,
        sellerName: event.userName,
        sellerPhone: event.userPhone,
      );

      emit(MarketStateProductAdded(product));
    } on FirebaseException catch (e) {
      emit(MarketStateError('فشل في إضافة المنتج: ${e.message}'));
    } catch (e) {
      emit(MarketStateError('فشل في إضافة المنتج: ${e.toString()}'));
    }
  }

  Future<void> _onLoadUserProducts(
      MarketEventLoadUserProducts event, Emitter<MarketState> emit) async {
    emit(MarketStateLoading());
    try {
      final products = await marketRepository.getUserProducts(event.userId);
      emit(MarketStateUserProductsLoaded(products));
    } catch (e) {
      emit(MarketStateError('Failed to load user products: ${e.toString()}'));
    }
  }

  Future<void> _onUpdateProduct(
      MarketEventUpdateProduct event, Emitter<MarketState> emit) async {
    emit(MarketStateLoading());
    try {
      final product = await marketRepository.updateProduct(
        id: event.id,
        name: event.name,
        description: event.description,
        price: event.price,
        unit: event.unit,
        quantity: event.quantity,
        category: event.category,
        imageUrls: event.imageUrls,
        location: event.location,
        sellerPhone: event.sellerPhone,
      );

      emit(MarketStateProductUpdated(product));
    } catch (e) {
      emit(MarketStateError('Failed to update product: ${e.toString()}'));
    }
  }

  Future<void> _onDeleteProduct(
      MarketEventDeleteProduct event, Emitter<MarketState> emit) async {
    emit(MarketStateLoading());
    try {
      await marketRepository.deleteProduct(event.id);
      emit(MarketStateProductDeleted(event.id));
    } catch (e) {
      emit(MarketStateError('Failed to delete product: ${e.toString()}'));
    }
  }

  Future<void> _onLoadChats(
      MarketEventLoadChats event, Emitter<MarketState> emit) async {
    emit(MarketStateLoading());
    try {
      final chats = await marketRepository.getChats(event.userId);
      emit(MarketStateChatsLoaded(chats));
    } on FirebaseException catch (e) {
      if (e.code == 'failed-precondition' &&
          e.message != null &&
          e.message!.contains('index')) {
        final indexUrl = _extractIndexUrl(e.message!);
        emit(MarketStateError(
            'Database index required. ${indexUrl != null ? 'You can create it from the link sent to the developer.' : ''}'));
      } else {
        emit(MarketStateError('Failed to load chats: ${e.message}'));
      }
    } catch (e) {
      emit(MarketStateError('Failed to load chats: ${e.toString()}'));
    }
  }

  Future<void> _onLoadMessages(
      MarketEventLoadMessages event, Emitter<MarketState> emit) async {
    emit(MarketStateLoading());
    try {
      final messages = await marketRepository.getMessages(event.chatId);
      emit(MarketStateMessagesLoaded(messages: messages, chatId: event.chatId));
    } on FirebaseException catch (e) {
      if (e.code == 'failed-precondition' &&
          e.message != null &&
          e.message!.contains('index')) {
        final indexUrl = _extractIndexUrl(e.message!);
        emit(MarketStateError(
            'Database index required. ${indexUrl != null ? 'You can create it from the link sent to the developer.' : ''}'));
      } else {
        emit(MarketStateError('Failed to load messages: ${e.message}'));
      }
    } catch (e) {
      emit(MarketStateError('Failed to load messages: ${e.toString()}'));
    }
  }

  String? _extractIndexUrl(String errorMessage) {
    final urlRegex = RegExp(r'https?://[^\s]+');
    final match = urlRegex.firstMatch(errorMessage);
    return match?.group(0);
  }

  Future<void> _onSendMessage(
      MarketEventSendMessage event, Emitter<MarketState> emit) async {
    emit(MarketStateLoading());
    try {
      List<String>? attachmentUrls;
      if (event.attachments != null && event.attachments!.isNotEmpty) {
        attachmentUrls = await marketRepository.uploadMessageAttachments(
          event.attachments!,
          event.chatId,
        );
      }

      final message = await marketRepository.sendMessage(
        chatId: event.chatId,
        content: event.content,
        senderId: event.senderId,
        participants: event.participants,
        attachmentUrls: attachmentUrls,
        attachmentType: event.attachmentType,
      );

      emit(MarketStateMessageSent(message));
    } on FirebaseException catch (e) {
      if (e.code == 'failed-precondition' &&
          e.message != null &&
          e.message!.contains('index')) {
        final indexUrl = _extractIndexUrl(e.message!);
        emit(MarketStateError(
            'Database index required. ${indexUrl != null ? 'You can create it from the link sent to the developer.' : ''}'));
      } else {
        emit(MarketStateError('Failed to send message: ${e.message}'));
      }
    } catch (e) {
      emit(MarketStateError('Failed to send message: ${e.toString()}'));
    }
  }
}
