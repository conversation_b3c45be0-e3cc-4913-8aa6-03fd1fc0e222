import 'package:flutter/material.dart';
import 'package:sam05/bloc/auth_bloc.dart';
import 'package:sam05/bloc/market_bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:image_picker/image_picker.dart';


import 'auth_screens.dart';
import 'settings_screen.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        if (state is AuthStateAuthenticated) {
          return _buildAuthenticatedProfile(context, state);
        } else {
          return _buildUnauthenticatedProfile(context);
        }
      },
    );
  }

  Widget _buildAuthenticatedProfile(
      BuildContext context, AuthStateAuthenticated state) {
    final user = state.user;

    // تحميل منتجات المستخدم
    context.read<MarketBloc>().add(MarketEventLoadUserProducts(user.id));

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          // إعادة تحميل منتجات المستخدم
          context.read<MarketBloc>().add(MarketEventLoadUserProducts(user.id));
        },
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // بطاقة الملف الشخصي
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // صورة الملف الشخصي
                        Stack(
                          alignment: Alignment.bottomRight,
                          children: [
                            CircleAvatar(
                              radius: 48,
                              backgroundColor: Theme.of(context)
                                  .primaryColor
                                  .withOpacity(0.1),
                              backgroundImage: user.imageUrl != null &&
                                      user.imageUrl!.isNotEmpty
                                  ? NetworkImage(user.imageUrl!)
                                  : const NetworkImage(
                                      'https://picsum.photos/id/1025/200'),
                              child: user.imageUrl == null ||
                                      user.imageUrl!.isEmpty
                                  ? Icon(
                                      Icons.person,
                                      size: 48,
                                      color: Theme.of(context).primaryColor,
                                    )
                                  : null,
                            ),
                            Container(
                              decoration: BoxDecoration(
                                color: Theme.of(context).primaryColor,
                                shape: BoxShape.circle,
                              ),
                              child: IconButton(
                                icon: const Icon(Icons.camera_alt,
                                    color: Colors.white, size: 20),
                                onPressed: () async {
                                  // تنفيذ منطق تغيير الصورة
                                  final scaffoldMessenger =
                                      ScaffoldMessenger.of(context);
                                  final ImagePicker picker = ImagePicker();
                                  final image = await picker.pickImage(
                                      source: ImageSource.gallery);
                                  if (image != null) {
                                    // تنفيذ رفع الصورة (محاكاة فقط)
                                    scaffoldMessenger.showSnackBar(
                                      const SnackBar(
                                          content:
                                              Text('جاري تحميل الصورة...')),
                                    );
                                  }
                                },
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(width: 16),

                        // معلومات المستخدم
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                user.name,
                                style: const TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'مزارع',
                                style: TextStyle(
                                  color: Theme.of(context)
                                      .colorScheme
                                      .onSurface
                                      .withOpacity(0.6),
                                ),
                              ),
                              if (user.phone != null) ...[
                                const SizedBox(height: 4),
                                Text(
                                  user.phone!,
                                  style: TextStyle(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface
                                        .withOpacity(0.6),
                                  ),
                                ),
                              ],
                              const SizedBox(height: 16),

                              // أزرار الإجراءات
                              Row(
                                children: [
                                  Expanded(
                                    child: ElevatedButton.icon(
                                      icon: const Icon(Icons.edit, size: 16),
                                      label: const Text('تعديل الملف'),
                                      style: ElevatedButton.styleFrom(
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 8),
                                      ),
                                      onPressed: () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) =>
                                                EditProfileScreen(user: user),
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  OutlinedButton.icon(
                                    icon: const Icon(Icons.share, size: 16),
                                    label: const Text('مشاركة'),
                                    style: OutlinedButton.styleFrom(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 8),
                                    ),
                                    onPressed: () {
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        const SnackBar(
                                            content: Text(
                                                'مشاركة الملف الشخصي قيد التطوير')),
                                      );
                                    },
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    const Divider(),
                    const SizedBox(height: 8),

                    // إحصائيات الملف
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildStatItem(context, '3', 'أنظمة الري'),
                        _buildStatItem(context, '0', 'المنتجات'),
                        _buildStatItem(context, '120', 'المتابعين'),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // قسم المنتجات
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          FontAwesomeIcons.store,
                          size: 18,
                          color: Theme.of(context).primaryColor,
                        ),
                        const SizedBox(width: 8),
                        const Text(
                          'المنتجات',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Spacer(),
                        TextButton(
                          onPressed: () {
                            // عرض كل المنتجات
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                  content: Text('عرض كل المنتجات قيد التطوير')),
                            );
                          },
                          child: const Text('عرض الكل'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // عرض المنتجات
                    BlocBuilder<MarketBloc, MarketState>(
                      builder: (context, state) {
                        if (state is MarketStateUserProductsLoaded) {
                          final products = state.products;

                          if (products.isEmpty) {
                            return Row(
                              children: [
                                _buildProductItem(
                                  context,
                                  imageUrl: 'https://picsum.photos/id/292/200',
                                  name: 'بذور قمح',
                                  price: '5000 ريال/كجم',
                                ),
                                const SizedBox(width: 12),
                                _buildProductItem(
                                  context,
                                  imageUrl: 'https://picsum.photos/id/209/200',
                                  name: 'طماطم طازجة',
                                  price: '1200 ريال/كجم',
                                ),
                              ],
                            );
                          }

                          return Row(
                            children: List.generate(
                              products.length > 2 ? 2 : products.length,
                              (index) => Expanded(
                                child: Padding(
                                  padding: EdgeInsets.only(
                                    right: index == 0 ? 0 : 6,
                                    left: index == 0 ? 6 : 0,
                                  ),
                                  child: _buildProductItem(
                                    context,
                                    imageUrl:
                                        products[index].imageUrls.isNotEmpty
                                            ? products[index].imageUrls.first
                                            : '',
                                    name: products[index].name,
                                    price:
                                        '${products[index].price} ريال/${products[index].unit}',
                                  ),
                                ),
                              ),
                            ),
                          );
                        } else if (state is MarketStateLoading) {
                          return const Center(
                              child: CircularProgressIndicator());
                        }

                        return Row(
                          children: [
                            _buildProductItem(
                              context,
                              imageUrl: 'https://picsum.photos/id/292/200',
                              name: 'بذور قمح',
                              price: '5000 ريال/كجم',
                            ),
                            const SizedBox(width: 12),
                            _buildProductItem(
                              context,
                              imageUrl: 'https://picsum.photos/id/209/200',
                              name: 'طماطم طازجة',
                              price: '1200 ريال/كجم',
                            ),
                          ],
                        );
                      },
                    ),
                    const SizedBox(height: 16),

                    // زر إضافة منتج
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton.icon(
                        icon: const Icon(Icons.add),
                        label: const Text('إضافة منتج'),
                        onPressed: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                                content: Text('إضافة منتج جديد قيد التطوير')),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // زر الإعدادات
            Card(
              child: InkWell(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const SettingsScreen()),
                  );
                },
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    children: [
                      Icon(
                        Icons.settings,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                      const SizedBox(width: 16),
                      const Text(
                        'الإعدادات',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      const Icon(Icons.chevron_right),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUnauthenticatedProfile(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.account_circle,
            size: 100,
            color: Theme.of(context).primaryColor.withOpacity(0.5),
          ),
          const SizedBox(height: 24),
          const Text(
            'لم تقم بتسجيل الدخول',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            'قم بتسجيل الدخول للوصول إلى ملفك الشخصي',
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(builder: (context) => const LoginScreen()),
              );
            },
            child: const Text('تسجيل الدخول'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, String value, String label) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
          ),
        ),
      ],
    );
  }

  Widget _buildProductItem(
    BuildContext context, {
    required String imageUrl,
    required String name,
    required String price,
  }) {
    return Expanded(
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.withOpacity(0.2)),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
              child: Image.network(
                imageUrl,
                height: 120,
                width: double.infinity,
                fit: BoxFit.cover,
                errorBuilder: (_, __, ___) => Container(
                  height: 120,
                  width: double.infinity,
                  color: Colors.grey[300],
                  child: const Icon(Icons.image, color: Colors.grey),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    price,
                    style: TextStyle(
                      color: Colors.green[700],
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
