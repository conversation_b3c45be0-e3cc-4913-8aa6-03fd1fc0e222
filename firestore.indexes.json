{"indexes": [{"collectionGroup": "irrigation_systems", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "lastUpdate", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "irrigation_systems", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "irrigation_systems", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "lastUpdate", "order": "DESCENDING"}]}, {"collectionGroup": "water_usage_stats", "queryScope": "COLLECTION", "fields": [{"fieldPath": "systemId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "water_usage_stats", "queryScope": "COLLECTION", "fields": [{"fieldPath": "systemId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "ASCENDING"}]}, {"collectionGroup": "irrigation_records", "queryScope": "COLLECTION", "fields": [{"fieldPath": "systemId", "order": "ASCENDING"}, {"fieldPath": "startTime", "order": "DESCENDING"}]}, {"collectionGroup": "irrigation_records", "queryScope": "COLLECTION", "fields": [{"fieldPath": "systemId", "order": "ASCENDING"}, {"fieldPath": "startTime", "order": "ASCENDING"}]}, {"collectionGroup": "products", "queryScope": "COLLECTION", "fields": [{"fieldPath": "category", "order": "ASCENDING"}, {"fieldPath": "isAvailable", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "products", "queryScope": "COLLECTION", "fields": [{"fieldPath": "sellerId", "order": "ASCENDING"}, {"fieldPath": "isAvailable", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "sensor_readings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "systemId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "sensor_readings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "systemId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "ASCENDING"}]}, {"collectionGroup": "irrigation_logs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "systemId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "irrigation_logs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "chatId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "ASCENDING"}]}, {"collectionGroup": "chats", "queryScope": "COLLECTION", "fields": [{"fieldPath": "participants", "arrayConfig": "CONTAINS"}, {"fieldPath": "lastMessageTime", "order": "DESCENDING"}]}], "fieldOverrides": []}