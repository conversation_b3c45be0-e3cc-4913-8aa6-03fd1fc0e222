import 'package:flutter_bloc/flutter_bloc.dart';
import "package:equatable/equatable.dart";
import 'package:shared_preferences/shared_preferences.dart';

abstract class ThemeEvent extends Equatable {
  const ThemeEvent();

  @override
  List<Object?> get props => [];
}

class ThemeEventToggle extends ThemeEvent {}

class ThemeState extends Equatable {
  final bool isDark;

  const ThemeState({required this.isDark});

  @override
  List<Object?> get props => [isDark];
}

class ThemeBloc extends Bloc<ThemeEvent, ThemeState> {
  final SharedPreferences prefs;

  ThemeBloc({
    required this.prefs,
    required bool initialIsDark,
  }) : super(ThemeState(isDark: initialIsDark)) {
    on<ThemeEventToggle>(_onToggle);
  }

  Future<void> _onToggle(ThemeEventToggle event, Emitter<ThemeState> emit) async {
    final newIsDark = !state.isDark;
    await prefs.setBool('isDarkMode', newIsDark);
    emit(ThemeState(isDark: newIsDark));
  }
}