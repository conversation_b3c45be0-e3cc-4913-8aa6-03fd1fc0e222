rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read and write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Products are readable by all authenticated users
    match /products/{productId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == resource.data.sellerId;
    }
    
    // Irrigation systems are owned by users
    match /irrigation_systems/{systemId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
    }
    
    // Sensor readings are readable by system owners
    match /sensor_readings/{readingId} {
      allow read, write: if request.auth != null;
    }
    
    // Irrigation records are readable by system owners
    match /irrigation_records/{recordId} {
      allow read, write: if request.auth != null;
    }
    
    // Water usage stats are readable by system owners
    match /water_usage_stats/{statsId} {
      allow read, write: if request.auth != null;
    }
    
    // Irrigation logs are readable by system owners
    match /irrigation_logs/{logId} {
      allow read, write: if request.auth != null;
    }
    
    // Messages and chats
    match /messages/{messageId} {
      allow read, write: if request.auth != null;
    }
    
    match /chats/{chatId} {
      allow read, write: if request.auth != null && request.auth.uid in resource.data.participants;
    }
  }
}
