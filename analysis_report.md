# تقرير تحليل المشروع: الميزات والخصائص الناقصة

## مقدمة
يقدم هذا التقرير تحليلاً شاملاً للميزات الحالية والناقصة في المكونات الرئيسية للمشروع: **نظام الري الذكي** و **نظام تشخيص أمراض النباتات**. الهدف هو تحديد فرص التحسين والتطوير لتقديم تجربة مستخدم متكاملة وفعالة.

---

## 1. نظام الري الذكي (`smart_irrigation_screen.dart`)

النظام الحالي يوفر واجهة أساسية لعرض بيانات الحساسات والتحكم اليدوي في الري. ومع ذلك، تنقصه العديد من الميزات ليصبح نظاماً "ذكياً" بالكامل.

### الميزات والخصائص الناقصة:

*   **إدارة أنظمة الري:**
    *   **إضافة/تعديل/حذف الأنظمة:** لا توجد حالياً وظيفة لإضافة أنظمة ري جديدة أو تعديل إعدادات الأنظمة الحالية أو حذفها من قبل المستخدم. زر "إضافة نظام" موجود ولكنه غير فعال.

*   **الري التلقائي (Automatic Irrigation):**
    *   **منطق الري التلقائي:** النظام يفتقر إلى القدرة على بدء وإيقاف الري تلقائياً بناءً على عتبات محددة مسبقاً (مثل: "ابدأ الري عندما تنخفض رطوبة التربة عن 40%").
    *   **تخصيص العتبات:** يجب أن يتمكن المستخدم من ضبط حدود الرطوبة ودرجة الحرارة التي يتم عندها تفعيل الري التلقائي.

*   **الإشعارات والتنبيهات (Push Notifications):**
    *   **تنبيهات مخصصة:** إرسال إشعارات للمستخدم في الحالات الهامة مثل:
        *   انخفاض مستوى المياه في الخزان.
        *   بدء أو إيقاف عملية ري (تلقائي أو يدوي).
        *   حدوث خطأ في أحد الأنظمة.

---

## 2. نظام تشخيص أمراض النباتات (`plant_disease_screen.dart`)

النظام الحالي يوفر واجهة لاختيار نبات والتقاط صورة، ولكنه يعتمد على بيانات محاكاة (Hardcoded) ويفتقر إلى التكامل العميق.

### الميزات والخصائص الناقصة:

*   **التكامل مع قاعدة البيانات (Backend Integration):**
    *   **سجل التشخيص الفعلي:** سجل التشخيصات الحالية هو بيانات ثابتة ومؤقتة. يجب ربط النظام بقاعدة بيانات (مثل Firebase Firestore) لحفظ واسترجاع سجل التشخيصات الخاص بكل مستخدم بشكل دائم.

*   **إدارة الصور:**
    *   **تخزين صور المستخدم:** يجب حفظ الصور التي يلتقطها المستخدم (مثلاً في Firebase Storage) وربطها بنتيجة التشخيص الخاصة بها في السجل.

*   **معلومات العلاج المتكاملة:**
    *   **توصيات العلاج:** بعد تشخيص المرض، يجب أن يقدم التطبيق معلومات مفصلة عن طرق العلاج، المبيدات الموصى بها، والجرعات المناسبة.
    *   **الربط بشاشة العلاج:** يجب تفعيل الانتقال من نتيجة التشخيص إلى شاشة `disease_treatment_screen.dart` لعرض هذه التفاصيل.

*   **تحسين تجربة المستخدم:**
    *   **ملاحظات المستخدم على التشخيص:** إضافة ميزة تسمح للمستخدم بتقييم دقة التشخيص. هذه الملاحظات يمكن استخدامها لتحسين دقة النموذج في المستقبل.
    *   **قائمة نباتات ديناميكية:** بدلاً من قائمة النباتات الثابتة، يمكن جلبها من قاعدة بيانات خارجية لتسهيل إضافة نباتات جديدة دون الحاجة لتحديث التطبيق.

*   **وظائف إضافية:**
    *   **البحث في السجل:** إضافة إمكانية البحث والفلترة في سجل التشخيصات حسب نوع النبات، المرض، أو التاريخ.
    *   **الدعم بدون انترنت (Offline Support):** دراسة إمكانية إجراء التشخيصات الأساسية بدون اتصال بالإنترنت باستخدام نموذج TFLite المحلي، مع مزامنة النتائج عند عودة الاتصال.