import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:sam05/models/app_models.dart';
import 'package:sam05/repositories/irrigation_repository.dart';
import 'package:sam05/core/constants/app_constants.dart';

/// خدمة الري الذكي المتقدمة
class SmartIrrigationService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final IrrigationRepository _irrigationRepository = IrrigationRepository();

  Timer? _monitoringTimer;
  final Map<String, StreamSubscription> _systemSubscriptions = {};

  /// بدء مراقبة أنظمة الري
  void startMonitoring(String userId) {
    print('🔄 بدء مراقبة أنظمة الري للمستخدم: $userId');

    // مراقبة كل 5 ثوانٍ
    _monitoringTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      _checkAllSystems(userId);
    });
  }

  /// إيقاف المراقبة
  void stopMonitoring() {
    _monitoringTimer?.cancel();
    for (final subscription in _systemSubscriptions.values) {
      subscription.cancel();
    }
    _systemSubscriptions.clear();
    print('⏹️ تم إيقاف مراقبة أنظمة الري');
  }

  /// فحص جميع الأنظمة
  Future<void> _checkAllSystems(String userId) async {
    try {
      final systems = await _irrigationRepository.getIrrigationSystems(userId);

      for (final system in systems) {
        await _checkSystemForAutoIrrigation(system);
      }
    } catch (e) {
      print('❌ خطأ في فحص الأنظمة: $e');
    }
  }

  /// فحص نظام واحد للري التلقائي
  Future<void> _checkSystemForAutoIrrigation(IrrigationSystem system) async {
    try {
      // جلب إعدادات النظام
      final settings =
          await _irrigationRepository.getIrrigationSettings(system.id);
      if (settings == null) return;

      // التحقق من تفعيل الري التلقائي أو الذكي
      if (!settings.autoModeEnabled && !settings.smartModeEnabled) return;

      // التحقق من الحالة الحالية
      if (system.currentIrrigationStatus == IrrigationStatus.running) return;

      // فحص الشروط
      final shouldIrrigate = await _shouldStartIrrigation(system, settings);

      if (shouldIrrigate) {
        final irrigationType = settings.smartModeEnabled
            ? IrrigationType.smart
            : IrrigationType.automatic;

        await startIrrigation(
          systemId: system.id,
          userId: system.userId,
          type: irrigationType,
          duration: settings.defaultDuration,
        );
      }
    } catch (e) {
      print('❌ خطأ في فحص النظام ${system.name}: $e');
    }
  }

  /// تحديد ما إذا كان يجب بدء الري
  Future<bool> _shouldStartIrrigation(
    IrrigationSystem system,
    IrrigationSettings settings,
  ) async {
    // فحص حساس المطر
    if (settings.rainSensorEnabled && system.sensors.rainStatus) {
      print('🌧️ تم إلغاء الري بسبب المطر - النظام: ${system.name}');
      return false;
    }

    // فحص الوقت المسموح
    if (!_isAllowedTime(settings.allowedTimeSlots)) {
      return false;
    }

    // فحص عدد مرات الري اليومية
    final todayIrrigations = await _getTodayIrrigationCount(system.id);
    if (todayIrrigations >= settings.maxDailyIrrigations) {
      return false;
    }

    // فحص رطوبة التربة
    if (system.sensors.soilMoisture > settings.moistureThreshold) {
      return false;
    }

    // للري الذكي: فحص متطلبات النبات
    if (settings.smartModeEnabled && settings.plantTypeId != null) {
      return await _checkSmartIrrigationConditions(system, settings);
    }

    // للري التلقائي: فحص العتبات البسيطة
    return _checkAutoIrrigationConditions(system, settings);
  }

  /// فحص شروط الري الذكي
  Future<bool> _checkSmartIrrigationConditions(
    IrrigationSystem system,
    IrrigationSettings settings,
  ) async {
    try {
      // جلب معلومات النبات
      final plantType =
          await _irrigationRepository.getPlantType(settings.plantTypeId!);
      if (plantType == null) return false;

      // فحص الرطوبة المثلى للنبات
      if (system.sensors.soilMoisture >= plantType.optimalMoisture) {
        return false;
      }

      // فحص درجة الحرارة
      if (system.sensors.temperature < plantType.minTemperature ||
          system.sensors.temperature > plantType.maxTemperature) {
        print('🌡️ درجة الحرارة خارج النطاق المثلى للنبات');
        return false;
      }

      // حساب الاحتياج المائي بناءً على الظروف
      final waterNeed = _calculateWaterNeed(system, plantType);

      print('💧 الاحتياج المائي المحسوب: ${waterNeed.toStringAsFixed(1)} لتر');

      return waterNeed > 0;
    } catch (e) {
      print('❌ خطأ في فحص شروط الري الذكي: $e');
      return false;
    }
  }

  /// فحص شروط الري التلقائي
  bool _checkAutoIrrigationConditions(
    IrrigationSystem system,
    IrrigationSettings settings,
  ) {
    // فحص رطوبة التربة
    if (system.sensors.soilMoisture >= settings.moistureThreshold) {
      return false;
    }

    // فحص درجة الحرارة
    if (system.sensors.temperature > settings.temperatureThreshold) {
      print('🔥 درجة الحرارة مرتفعة جداً للري');
      return false;
    }

    return true;
  }

  /// حساب الاحتياج المائي للنبات
  double _calculateWaterNeed(IrrigationSystem system, PlantType plantType) {
    // الاحتياج الأساسي
    double baseNeed = plantType.waterRequirementPerDay;

    // تعديل حسب درجة الحرارة
    double tempFactor = 1.0;
    if (system.sensors.temperature > 30) {
      tempFactor = 1.3; // زيادة 30% في الحر
    } else if (system.sensors.temperature < 20) {
      tempFactor = 0.8; // تقليل 20% في البرد
    }

    // تعديل حسب الرطوبة الجوية
    double humidityFactor = 1.0;
    if (system.sensors.humidity < 0.4) {
      humidityFactor = 1.2; // زيادة في الجفاف
    } else if (system.sensors.humidity > 0.8) {
      humidityFactor = 0.9; // تقليل في الرطوبة العالية
    }

    // تعديل حسب رطوبة التربة
    double soilMoistureFactor = 1.0;
    if (system.sensors.soilMoisture < plantType.optimalMoisture * 0.5) {
      soilMoistureFactor = 1.5; // زيادة كبيرة للتربة الجافة جداً
    } else if (system.sensors.soilMoisture < plantType.optimalMoisture * 0.8) {
      soilMoistureFactor = 1.2; // زيادة متوسطة
    }

    return baseNeed * tempFactor * humidityFactor * soilMoistureFactor;
  }

  /// التحقق من الوقت المسموح
  bool _isAllowedTime(List<String> allowedTimeSlots) {
    final now = DateTime.now();
    final currentTime =
        '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';

    for (final slot in allowedTimeSlots) {
      final parts = slot.split('-');
      if (parts.length == 2) {
        final start = parts[0];
        final end = parts[1];

        if (_isTimeBetween(currentTime, start, end)) {
          return true;
        }
      }
    }

    return false;
  }

  /// التحقق من وقوع الوقت بين فترتين
  bool _isTimeBetween(String current, String start, String end) {
    final currentMinutes = _timeToMinutes(current);
    final startMinutes = _timeToMinutes(start);
    final endMinutes = _timeToMinutes(end);

    if (startMinutes <= endMinutes) {
      return currentMinutes >= startMinutes && currentMinutes <= endMinutes;
    } else {
      // الفترة تمتد عبر منتصف الليل
      return currentMinutes >= startMinutes || currentMinutes <= endMinutes;
    }
  }

  /// تحويل الوقت إلى دقائق
  int _timeToMinutes(String time) {
    final parts = time.split(':');
    return int.parse(parts[0]) * 60 + int.parse(parts[1]);
  }

  /// جلب عدد مرات الري اليوم
  Future<int> _getTodayIrrigationCount(String systemId) async {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    final snapshot = await _firestore
        .collection('irrigation_logs')
        .where('systemId', isEqualTo: systemId)
        .where('startTime',
            isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
        .where('startTime', isLessThan: Timestamp.fromDate(endOfDay))
        .get();

    return snapshot.docs.length;
  }

  /// بدء الري
  Future<void> startIrrigation({
    required String systemId,
    required String userId,
    required IrrigationType type,
    required int duration,
    Map<String, dynamic>? parameters,
  }) async {
    try {
      print(
          '🚿 بدء الري - النظام: $systemId، النوع: $type، المدة: $duration دقيقة');

      // إنشاء أمر الري
      final command = IrrigationCommand(
        id: '',
        systemId: systemId,
        userId: userId,
        command: 'start',
        type: type,
        duration: duration,
        parameters: parameters,
        timestamp: DateTime.now(),
        executed: false,
      );

      // حفظ الأمر في قاعدة البيانات
      await _firestore
          .collection('irrigation_commands')
          .add(command.toFirestore());

      // تحديث حالة النظام
      await _firestore.collection('irrigation_systems').doc(systemId).update({
        'currentIrrigationStatus':
            IrrigationStatus.running.toString().split('.').last,
        'lastUpdate': FieldValue.serverTimestamp(),
      });

      // إنشاء سجل الري
      await _createIrrigationLog(systemId, userId, type, duration);
    } catch (e) {
      print('❌ خطأ في بدء الري: $e');
      rethrow;
    }
  }

  /// إيقاف الري
  Future<void> stopIrrigation({
    required String systemId,
    required String userId,
  }) async {
    try {
      print('⏹️ إيقاف الري - النظام: $systemId');

      // إنشاء أمر الإيقاف
      final command = IrrigationCommand(
        id: '',
        systemId: systemId,
        userId: userId,
        command: 'stop',
        type: IrrigationType.manual,
        timestamp: DateTime.now(),
        executed: false,
      );

      await _firestore
          .collection('irrigation_commands')
          .add(command.toFirestore());

      // تحديث حالة النظام
      await _firestore.collection('irrigation_systems').doc(systemId).update({
        'currentIrrigationStatus':
            IrrigationStatus.idle.toString().split('.').last,
        'lastUpdate': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      print('❌ خطأ في إيقاف الري: $e');
      rethrow;
    }
  }

  /// إنشاء سجل الري
  Future<void> _createIrrigationLog(
    String systemId,
    String userId,
    IrrigationType type,
    int duration,
  ) async {
    // جلب بيانات الحساسات الحالية
    final systemDoc = await _firestore
        .collection(AppConstants.collectionIrrigationSystems)
        .doc(systemId)
        .get();
    final systemData = systemDoc.data();

    final log = IrrigationLog(
      id: '',
      systemId: systemId,
      userId: userId,
      type: type,
      startTime: DateTime.now(),
      duration: duration,
      waterUsed: duration * 2.0, // تقدير: 2 لتر/دقيقة
      status: IrrigationStatus.running,
      triggeredBy: type.toString().split('.').last,
      sensorDataAtStart: systemData?['sensors'],
      createdAt: DateTime.now(),
    );

    await _firestore.collection('irrigation_logs').add(log.toFirestore());
  }
}
