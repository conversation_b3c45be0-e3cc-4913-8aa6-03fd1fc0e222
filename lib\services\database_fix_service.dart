import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:sam05/core/constants/app_constants.dart';
import 'package:sam05/services/user_service.dart';
import 'package:sam05/models/app_models.dart';

/// خدمة إصلاح وتحسين قاعدة البيانات
class DatabaseFixService {
  static final DatabaseFixService _instance = DatabaseFixService._internal();
  factory DatabaseFixService() => _instance;
  DatabaseFixService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final UserService _userService = UserService();

  /// إصلاح شامل لقاعدة البيانات
  Future<List<String>> performComprehensiveFix() async {
    try {
      print('🔧 بدء الإصلاح الشامل لقاعدة البيانات...');

      // 1. التحقق من الاتصال
      await _checkConnection();

      // 2. إنشاء بيانات المستخدم التجريبي
      await _createDemoUserData();

      // 3. إنشاء أنظمة ري تجريبية
      await _createDemoIrrigationSystems();

      // 4. التحقق من الفهارس وجمع الروابط المفقودة
      final missingIndexUrls = await _verifyIndexes();

      // 5. تنظيف البيانات القديمة
      await _cleanupOldData();

      if (missingIndexUrls.isEmpty) {
        print('✅ تم الإصلاح الشامل بنجاح!');
      } else {
        print('⚠️ تم الإصلاح مع وجود فهارس مفقودة');
        print('📋 يرجى إنشاء الفهارس باستخدام الروابط المعروضة');
      }

      return missingIndexUrls;
    } catch (e) {
      print('❌ خطأ في الإصلاح الشامل: $e');
      throw Exception('فشل في إصلاح قاعدة البيانات: $e');
    }
  }

  /// التحقق من الاتصال بقاعدة البيانات
  Future<void> _checkConnection() async {
    try {
      await _firestore
          .collection('test_connection')
          .doc('test')
          .set({'timestamp': Timestamp.now()});

      await _firestore.collection('test_connection').doc('test').delete();

      print('✅ الاتصال بقاعدة البيانات سليم');
    } catch (e) {
      throw Exception('فشل في الاتصال بقاعدة البيانات: $e');
    }
  }

  /// إنشاء بيانات المستخدم التجريبي
  Future<void> _createDemoUserData() async {
    try {
      final userId = _userService.getCurrentUserId() ?? 'demo_user';

      final userDoc = await _firestore.collection('users').doc(userId).get();

      if (!userDoc.exists) {
        await _firestore.collection('users').doc(userId).set({
          'id': userId,
          'name': 'مستخدم تجريبي',
          'email': '<EMAIL>',
          'phone': '+966500000000',
          'location': 'الرياض، المملكة العربية السعودية',
          'farmName': 'مزرعة تجريبية',
          'farmSize': 1000.0,
          'cropTypes': ['خضروات', 'فواكه'],
          'irrigationExperience': 'متوسط',
          'createdAt': Timestamp.now(),
          'lastLogin': Timestamp.now(),
          'isActive': true,
          'preferences': {
            'language': 'ar',
            'notifications': true,
            'autoIrrigation': true,
            'smartMode': true,
            'theme': 'light',
          },
          'subscription': {
            'type': 'free',
            'startDate': Timestamp.now(),
            'endDate': Timestamp.fromDate(
                DateTime.now().add(const Duration(days: 30))),
            'maxSystems': 5,
            'features': [
              'basic_monitoring',
              'manual_irrigation',
              'basic_reports',
              'realtime_sensors'
            ],
          },
        });

        print('✅ تم إنشاء بيانات المستخدم التجريبي');
      } else {
        print('✅ بيانات المستخدم موجودة مسبقاً');
      }
    } catch (e) {
      print('❌ خطأ في إنشاء بيانات المستخدم: $e');
    }
  }

  /// إنشاء أنظمة ري تجريبية
  Future<void> _createDemoIrrigationSystems() async {
    try {
      final userId = _userService.getCurrentUserId() ?? 'demo_user';

      final systemsSnapshot = await _firestore
          .collection(AppConstants.collectionIrrigationSystems)
          .where('userId', isEqualTo: userId)
          .get();

      if (systemsSnapshot.docs.isEmpty) {
        // إنشاء 3 أنظمة ري تجريبية
        final demoSystems = [
          {
            'name': 'نظام ري الحديقة الأمامية',
            'type': 'drip',
            'serialNumber': 'IR-001',
            'description': 'نظام ري بالتنقيط للحديقة الأمامية',
            'location': 'الحديقة الأمامية',
            'cropType': 'خضروات',
          },
          {
            'name': 'نظام ري البستان',
            'type': 'sprinkler',
            'serialNumber': 'IR-002',
            'description': 'نظام ري بالرش للبستان',
            'location': 'البستان الخلفي',
            'cropType': 'فواكه',
          },
          {
            'name': 'نظام ري الصوبة الزراعية',
            'type': 'micro',
            'serialNumber': 'IR-003',
            'description': 'نظام ري دقيق للصوبة الزراعية',
            'location': 'الصوبة الزراعية',
            'cropType': 'أعشاب',
          },
        ];

        for (final systemData in demoSystems) {
          await _createDemoSystem(userId, systemData);
        }

        print('✅ تم إنشاء أنظمة الري التجريبية');
      } else {
        print('✅ أنظمة الري موجودة مسبقاً');
      }
    } catch (e) {
      print('❌ خطأ في إنشاء أنظمة الري: $e');
    }
  }

  /// إنشاء نظام ري تجريبي واحد
  Future<void> _createDemoSystem(
      String userId, Map<String, String> systemData) async {
    final now = DateTime.now();

    // بيانات حساسات فارغة - ستأتي من الأجهزة الفعلية
    final defaultSensors = {
      'soilMoisture': 0.0, // لا توجد قراءة
      'temperature': 0.0, // لا توجد قراءة
      'humidity': 0.0, // لا توجد قراءة
      'waterLevel': 0.0, // لا توجد قراءة
      'batteryLevel': 0.0, // لا توجد قراءة
      'rainStatus': false, // افتراضي: لا يوجد مطر
    };

    final systemDoc = {
      'userId': userId,
      'name': systemData['name'],
      'type': systemData['type'],
      'serialNumber': systemData['serialNumber'],
      'description': systemData['description'],
      'location': systemData['location'],
      'cropType': systemData['cropType'],
      'status': 'active',
      'currentIrrigationStatus': 'idle',
      'sensors': defaultSensors,
      'isOnline': true,
      'isIrrigating': false,
      'lastIrrigationTime': null,
      'totalWaterUsed': 0.0,
      'createdAt': Timestamp.fromDate(now),
      'lastUpdate': Timestamp.fromDate(now),
    };

    final docRef = await _firestore
        .collection(AppConstants.collectionIrrigationSystems)
        .add(systemDoc);

    // إنشاء إعدادات افتراضية للنظام
    await _firestore
        .collection(AppConstants.collectionIrrigationSettings)
        .doc(docRef.id)
        .set({
      'systemId': docRef.id,
      'moistureThreshold': 0.3,
      'temperatureThreshold': 35.0,
      'defaultDuration': 30,
      'maxWaterPerDay': 500.0,
      'autoIrrigationEnabled': true,
      'smartModeEnabled': true,
      'rainSensorEnabled': true,
      'maxDailyIrrigations': 3,
      'allowedTimeSlots': [
        {'start': '06:00', 'end': '08:00'},
        {'start': '18:00', 'end': '20:00'},
      ],
      'createdAt': Timestamp.fromDate(now),
      'updatedAt': Timestamp.fromDate(now),
    });
  }

  /// التحقق من الفهارس المطلوبة وعرض روابط الإنشاء
  Future<List<String>> _verifyIndexes() async {
    final missingIndexUrls = <String>[];
    final userId = _userService.getCurrentUserId() ?? 'demo_user';

    try {
      print('🔍 فحص الفهارس المطلوبة...');

      // اختبار فهرس أنظمة الري
      try {
        await _firestore
            .collection(AppConstants.collectionIrrigationSystems)
            .where('userId', isEqualTo: userId)
            .orderBy('lastUpdate', descending: true)
            .limit(1)
            .get();
        print('✅ فهرس أنظمة الري متوفر');
      } catch (e) {
        if (e.toString().contains('index')) {
          final url = _extractIndexUrl(e.toString());
          if (url != null) {
            missingIndexUrls.add(url);
            print('❌ فهرس أنظمة الري مفقود');
            print('🔗 رابط الإنشاء: $url');
            print('📋 المجموعة: ${AppConstants.collectionIrrigationSystems}');
            print('📋 الحقول: userId, lastUpdate');
          }
        }
      }

      // اختبار فهرس قراءات الحساسات
      try {
        await _firestore
            .collection(AppConstants.collectionSensorReadings)
            .where('systemId', isEqualTo: 'test_system')
            .orderBy('timestamp', descending: true)
            .limit(1)
            .get();
        print('✅ فهرس قراءات الحساسات متوفر');
      } catch (e) {
        if (e.toString().contains('index')) {
          final url = _extractIndexUrl(e.toString());
          if (url != null) {
            missingIndexUrls.add(url);
            print('❌ فهرس قراءات الحساسات مفقود');
            print('🔗 رابط الإنشاء: $url');
          }
        }
      }

      // اختبار فهرس سجلات الري
      try {
        await _firestore
            .collection(AppConstants.collectionIrrigationRecords)
            .where('systemId', isEqualTo: 'test_system')
            .orderBy('startTime', descending: true)
            .limit(1)
            .get();
        print('✅ فهرس سجلات الري متوفر');
      } catch (e) {
        if (e.toString().contains('index')) {
          final url = _extractIndexUrl(e.toString());
          if (url != null) {
            missingIndexUrls.add(url);
            print('❌ فهرس سجلات الري مفقود');
            print('🔗 رابط الإنشاء: $url');
          }
        }
      }

      if (missingIndexUrls.isEmpty) {
        print('✅ جميع الفهارس المطلوبة متوفرة');
      } else {
        print('⚠️ تم العثور على ${missingIndexUrls.length} فهرس مفقود');
        print('📋 يرجى إنشاء الفهارس باستخدام الروابط أعلاه');
      }

      return missingIndexUrls;
    } catch (e) {
      print('❌ خطأ في التحقق من الفهارس: $e');
      return missingIndexUrls;
    }
  }

  /// استخراج رابط إنشاء الفهرس من رسالة الخطأ
  String? _extractIndexUrl(String errorMessage) {
    final regex = RegExp(r'https://console\.firebase\.google\.com[^\s]+');
    final match = regex.firstMatch(errorMessage);
    return match?.group(0);
  }

  /// تنظيف البيانات القديمة
  Future<void> _cleanupOldData() async {
    try {
      final cutoffDate = DateTime.now().subtract(const Duration(days: 30));

      // حذف قراءات الحساسات القديمة
      final oldReadings = await _firestore
          .collection(AppConstants.collectionSensorReadings)
          .where('timestamp', isLessThan: Timestamp.fromDate(cutoffDate))
          .get();

      final batch = _firestore.batch();
      for (final doc in oldReadings.docs) {
        batch.delete(doc.reference);
      }

      if (oldReadings.docs.isNotEmpty) {
        await batch.commit();
        print('✅ تم تنظيف ${oldReadings.docs.length} قراءة قديمة');
      } else {
        print('✅ لا توجد بيانات قديمة للتنظيف');
      }
    } catch (e) {
      print('❌ خطأ في تنظيف البيانات: $e');
    }
  }

  /// إصلاح سريع للمشاكل الشائعة
  Future<void> quickFix() async {
    try {
      print('🔧 بدء الإصلاح السريع...');

      await _checkConnection();
      await _createDemoUserData();
      await _fixExistingSystemsData();

      print('✅ تم الإصلاح السريع بنجاح!');
    } catch (e) {
      print('❌ خطأ في الإصلاح السريع: $e');
      throw Exception('فشل في الإصلاح السريع: $e');
    }
  }

  /// إصلاح بيانات الأنظمة الموجودة
  Future<void> _fixExistingSystemsData() async {
    try {
      print('🔧 إصلاح بيانات الأنظمة الموجودة...');

      final systemsSnapshot = await _firestore
          .collection(AppConstants.collectionIrrigationSystems)
          .get();

      final batch = _firestore.batch();
      int fixedCount = 0;

      for (final doc in systemsSnapshot.docs) {
        final data = doc.data();
        bool needsUpdate = false;
        final updates = <String, dynamic>{};

        // إضافة الحقول المفقودة
        if (!data.containsKey('currentIrrigationStatus')) {
          updates['currentIrrigationStatus'] = 'idle';
          needsUpdate = true;
        }

        if (!data.containsKey('isOnline')) {
          updates['isOnline'] = true;
          needsUpdate = true;
        }

        if (!data.containsKey('createdAt')) {
          updates['createdAt'] = data['lastUpdate'] ?? Timestamp.now();
          needsUpdate = true;
        }

        // إصلاح بيانات الحساسات إذا كانت مفقودة أو غير صحيحة
        if (!data.containsKey('sensors') || data['sensors'] == null) {
          final defaultSensors = {
            'soilMoisture': 0.0,
            'temperature': 25.0,
            'humidity': 0.5,
            'waterLevel': 0.8,
            'batteryLevel': 1.0,
            'rainStatus': false,
          };
          updates['sensors'] = defaultSensors;
          needsUpdate = true;
        }

        if (needsUpdate) {
          batch.update(doc.reference, updates);
          fixedCount++;
        }
      }

      if (fixedCount > 0) {
        await batch.commit();
        print('✅ تم إصلاح $fixedCount نظام');
      } else {
        print('✅ جميع الأنظمة سليمة');
      }
    } catch (e) {
      print('❌ خطأ في إصلاح بيانات الأنظمة: $e');
    }
  }

  /// إعادة تعيين قاعدة البيانات (للاختبار فقط)
  Future<void> resetDatabase() async {
    try {
      print('🔄 بدء إعادة تعيين قاعدة البيانات...');

      final userId = _userService.getCurrentUserId() ?? 'demo_user';

      // حذف جميع أنظمة الري للمستخدم
      final systems = await _firestore
          .collection(AppConstants.collectionIrrigationSystems)
          .where('userId', isEqualTo: userId)
          .get();

      final batch = _firestore.batch();
      for (final doc in systems.docs) {
        batch.delete(doc.reference);
      }

      if (systems.docs.isNotEmpty) {
        await batch.commit();
      }

      // إعادة إنشاء البيانات التجريبية
      await _createDemoUserData();
      await _createDemoIrrigationSystems();

      print('✅ تم إعادة تعيين قاعدة البيانات بنجاح!');
    } catch (e) {
      print('❌ خطأ في إعادة تعيين قاعدة البيانات: $e');
      throw Exception('فشل في إعادة تعيين قاعدة البيانات: $e');
    }
  }
}
