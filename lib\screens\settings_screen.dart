import 'package:flutter/material.dart';
import 'package:sam05/bloc/auth_bloc.dart';
import 'package:sam05/bloc/theme_bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:image_picker/image_picker.dart';

import '../models/app_models.dart';

// شاشة تعديل الملف الشخصي
class EditProfileScreen extends StatefulWidget {
  final User user;

  const EditProfileScreen({super.key, required this.user});

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  final _formKey = GlobalKey<FormBuilderState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تعديل الملف الشخصي'),
      ),
      body: BlocListener<AuthBloc, AuthState>(
        listener: (context, state) {
          if (state is AuthStateError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(state.message)),
            );
          } else if (state is AuthStateAuthenticated) {
            Navigator.pop(context);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('تم تحديث الملف الشخصي بنجاح')),
            );
          }
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: FormBuilder(
            key: _formKey,
            initialValue: {
              'name': widget.user.name,
              'email': widget.user.email,
              'phone': widget.user.phone ?? '',
            },
            child: Column(
              children: [
                // الصورة الشخصية
                Center(
                  child: Stack(
                    alignment: Alignment.bottomRight,
                    children: [
                      CircleAvatar(
                        radius: 50,
                        backgroundImage: widget.user.imageUrl != null &&
                                widget.user.imageUrl!.isNotEmpty
                            ? NetworkImage(widget.user.imageUrl!)
                            : null,
                        child: widget.user.imageUrl == null ||
                                widget.user.imageUrl!.isEmpty
                            ? Text(
                                widget.user.name.substring(0, 1),
                                style: const TextStyle(fontSize: 36),
                              )
                            : null,
                      ),
                      Container(
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor,
                          shape: BoxShape.circle,
                        ),
                        child: IconButton(
                          icon: const Icon(Icons.camera_alt,
                              color: Colors.white, size: 20),
                          onPressed: () async {
                            // تنفيذ منطق تغيير الصورة
                            final scaffoldMessenger =
                                ScaffoldMessenger.of(context);
                            final ImagePicker picker = ImagePicker();
                            final image = await picker.pickImage(
                                source: ImageSource.gallery);
                            if (image != null && mounted) {
                              // تنفيذ رفع الصورة (محاكاة فقط)
                              scaffoldMessenger.showSnackBar(
                                const SnackBar(
                                    content: Text('جاري تحميل الصورة...')),
                              );
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),

                // حقول البيانات
                FormBuilderTextField(
                  name: 'name',
                  decoration: const InputDecoration(
                    labelText: 'الاسم',
                    prefixIcon: Icon(Icons.person),
                  ),
                  validator: FormBuilderValidators.compose([
                    FormBuilderValidators.required(
                        errorText: 'الرجاء إدخال الاسم'),
                  ]),
                ),
                const SizedBox(height: 16),

                FormBuilderTextField(
                  name: 'email',
                  keyboardType: TextInputType.emailAddress,
                  textDirection: TextDirection.ltr,
                  enabled: false, // لا يمكن تغيير البريد الإلكتروني
                  decoration: const InputDecoration(
                    labelText: 'البريد الإلكتروني',
                    prefixIcon: Icon(Icons.email),
                  ),
                ),
                const SizedBox(height: 16),

                FormBuilderTextField(
                  name: 'phone',
                  keyboardType: TextInputType.phone,
                  textDirection: TextDirection.ltr,
                  decoration: const InputDecoration(
                    labelText: 'رقم الهاتف',
                    prefixIcon: Icon(Icons.phone),
                    hintText: 'مثال: +967xxxxxxxxx',
                  ),
                ),
                const SizedBox(height: 24),

                // أزرار الإجراءات
                SizedBox(
                  width: double.infinity,
                  child: BlocBuilder<AuthBloc, AuthState>(
                    builder: (context, state) {
                      return ElevatedButton(
                        onPressed:
                            state is AuthStateLoading ? null : _updateProfile,
                        child: Padding(
                          padding: const EdgeInsets.all(12.0),
                          child: state is AuthStateLoading
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                )
                              : const Text(
                                  'حفظ التغييرات',
                                  style: TextStyle(fontSize: 16),
                                ),
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(height: 16),

                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: const Padding(
                      padding: EdgeInsets.all(12.0),
                      child: Text(
                        'إلغاء',
                        style: TextStyle(fontSize: 16),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _updateProfile() {
    if (_formKey.currentState?.saveAndValidate() ?? false) {
      final formData = _formKey.currentState!.value;

      final updatedUser = widget.user.copyWith(
        name: formData['name'],
        phone: formData['phone'].isEmpty ? null : formData['phone'],
      );

      context.read<AuthBloc>().add(AuthEventUpdateProfile(updatedUser));
    }
  }
}

// شاشة الإعدادات
class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  // إعدادات التطبيق
  String _selectedFontSize = 'متوسط';
  bool _notificationsEnabled = true;

  // إعدادات الري
  String _selectedWaterThreshold = '30%';
  bool _waterSavingMode = false;
  bool _stopIrrigationOnRain = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإعدادات'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          // إعدادات الحساب
          _buildSectionTitle(context, 'إعدادات الحساب'),
          Card(
            child: Column(
              children: [
                _buildSettingItem(
                  context,
                  icon: Icons.key,
                  title: 'تغيير كلمة المرور',
                  subtitle: 'تعديل كلمة المرور الخاصة بك',
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                          content: Text('تغيير كلمة المرور قيد التطوير')),
                    );
                  },
                ),
                const Divider(height: 1),
                _buildSettingItem(
                  context,
                  icon: Icons.person,
                  title: 'تعديل الملف الشخصي',
                  subtitle: 'تعديل معلوماتك الشخصية',
                  onTap: () {
                    Navigator.pop(context);
                  },
                ),
                const Divider(height: 1),
                _buildSettingItem(
                  context,
                  icon: Icons.location_on,
                  title: 'تغيير الموقع',
                  subtitle: 'تحديد موقع المزرعة',
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('تغيير الموقع قيد التطوير')),
                    );
                  },
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // إعدادات التطبيق
          _buildSectionTitle(context, 'إعدادات التطبيق'),
          Card(
            child: Column(
              children: [
                BlocBuilder<ThemeBloc, ThemeState>(
                  builder: (context, state) {
                    return _buildSwitchSettingItem(
                      context,
                      icon: Icons.dark_mode,
                      title: 'الوضع الليلي',
                      subtitle: 'تفعيل المظهر الداكن للتطبيق',
                      value: state.isDark,
                      onChanged: (value) {
                        context.read<ThemeBloc>().add(ThemeEventToggle());
                      },
                    );
                  },
                ),
                const Divider(height: 1),
                _buildDropdownSettingItem(
                  context,
                  icon: Icons.format_size,
                  title: 'حجم الخط',
                  subtitle: 'تغيير حجم النصوص في التطبيق',
                  value: _selectedFontSize,
                  items: const ['صغير', 'متوسط', 'كبير'],
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedFontSize = value;
                      });
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('تم تغيير حجم الخط إلى $value')),
                      );
                    }
                  },
                ),
                const Divider(height: 1),
                _buildSwitchSettingItem(
                  context,
                  icon: Icons.notifications,
                  title: 'الإشعارات',
                  subtitle: 'تفعيل إشعارات التطبيق',
                  value: _notificationsEnabled,
                  onChanged: (value) {
                    setState(() {
                      _notificationsEnabled = value;
                    });
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                          content: Text(value
                              ? 'تم تفعيل الإشعارات'
                              : 'تم إيقاف الإشعارات')),
                    );
                  },
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // إعدادات الري
          _buildSectionTitle(context, 'إعدادات الري'),
          Card(
            child: Column(
              children: [
                _buildDropdownSettingItem(
                  context,
                  icon: Icons.water_drop,
                  title: 'عتبة انخفاض المياه',
                  subtitle: 'الحد الأدنى لمستوى المياه قبل التنبيه',
                  value: _selectedWaterThreshold,
                  items: const ['20%', '30%', '40%'],
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedWaterThreshold = value;
                      });
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                            content:
                                Text('تم تغيير عتبة انخفاض المياه إلى $value')),
                      );
                    }
                  },
                ),
                const Divider(height: 1),
                _buildSwitchSettingItem(
                  context,
                  icon: Icons.eco,
                  title: 'وضع توفير المياه',
                  subtitle: 'تقليل استهلاك المياه في الري',
                  value: _waterSavingMode,
                  onChanged: (value) {
                    setState(() {
                      _waterSavingMode = value;
                    });
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                          content: Text(value
                              ? 'تم تفعيل وضع توفير المياه'
                              : 'تم إيقاف وضع توفير المياه')),
                    );
                  },
                ),
                const Divider(height: 1),
                _buildSwitchSettingItem(
                  context,
                  icon: Icons.cloud_outlined,
                  title: 'إيقاف الري عند المطر',
                  subtitle: 'إيقاف الري التلقائي عند هطول الأمطار',
                  value: _stopIrrigationOnRain,
                  onChanged: (value) {
                    setState(() {
                      _stopIrrigationOnRain = value;
                    });
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                          content: Text(value
                              ? 'تم تفعيل إيقاف الري عند المطر'
                              : 'تم إيقاف هذه الخاصية')),
                    );
                  },
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // حول التطبيق
          _buildSectionTitle(context, 'حول التطبيق'),
          Card(
            child: Column(
              children: [
                _buildSettingItem(
                  context,
                  icon: Icons.info,
                  title: 'الإصدار',
                  subtitle: '1.0.0',
                  showArrow: false,
                ),
                const Divider(height: 1),
                _buildSettingItem(
                  context,
                  icon: Icons.privacy_tip,
                  title: 'سياسة الخصوصية',
                  subtitle: 'قراءة سياسة الخصوصية',
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                          content: Text('سياسة الخصوصية قيد التطوير')),
                    );
                  },
                ),
                const Divider(height: 1),
                _buildSettingItem(
                  context,
                  icon: Icons.email,
                  title: 'تواصل مع المطورين',
                  subtitle: 'إرسال ملاحظات أو استفسارات',
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                          content: Text('التواصل مع المطورين قيد التطوير')),
                    );
                  },
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // زر تسجيل الخروج
          ElevatedButton.icon(
            icon: const Icon(Icons.logout),
            label: const Text('تسجيل الخروج'),
            style: ElevatedButton.styleFrom(
              foregroundColor: Colors.white,
              backgroundColor: Colors.red,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
            onPressed: () {
              _showLogoutConfirmation(context);
            },
          ),
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0, right: 4.0),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Theme.of(context).primaryColor,
        ),
      ),
    );
  }

  Widget _buildSettingItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    VoidCallback? onTap,
    bool showArrow = true,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
        child: Row(
          children: [
            Icon(
              icon,
              color: Theme.of(context).primaryColor,
              size: 24,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withOpacity(0.6),
                    ),
                  ),
                ],
              ),
            ),
            if (showArrow) const Icon(Icons.chevron_right, size: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildSwitchSettingItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      child: Row(
        children: [
          Icon(
            icon,
            color: Theme.of(context).primaryColor,
            size: 24,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withOpacity(0.6),
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: Theme.of(context).primaryColor,
          ),
        ],
      ),
    );
  }

  Widget _buildDropdownSettingItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required String value,
    required List<String> items,
    required ValueChanged<String?> onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      child: Row(
        children: [
          Icon(
            icon,
            color: Theme.of(context).primaryColor,
            size: 24,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withOpacity(0.6),
                  ),
                ),
              ],
            ),
          ),
          DropdownButton<String>(
            value: value,
            items: items.map((item) {
              return DropdownMenuItem<String>(
                value: item,
                child: Text(item),
              );
            }).toList(),
            onChanged: onChanged,
            underline: const SizedBox(),
          ),
        ],
      ),
    );
  }

  void _showLogoutConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من رغبتك في تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<AuthBloc>().add(AuthEventLogOut());
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }
}
