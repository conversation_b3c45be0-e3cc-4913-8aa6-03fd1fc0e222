# 🔥 دليل إعداد فهارس Firebase Firestore

## 🚨 حل مشكلة الفهرس المطلوب

عندما تظهر رسالة الخطأ التالية:
```
The query requires an [cloud_firestore/failed-precondition] index. 
You can create it here: https://console.firebase.google.com/...
```

## 📋 الحلول المتاحة

### 1. **الحل السريع - استخدام الرابط المباشر**

1. انسخ الرابط من رسالة الخطأ
2. افتح الرابط في المتصفح
3. اضغط على "Create Index"
4. انتظر حتى يكتمل إنشاء الفهرس (قد يستغرق بضع دقائق)

### 2. **الحل اليدوي - Firebase Console**

#### الخطوات:
1. اذهب إلى [Firebase Console](https://console.firebase.google.com)
2. اختر مشروعك `sam03-b6433`
3. اذهب إلى **Firestore Database**
4. اضغط على تبويب **Indexes**
5. اضغط على **Create Index**

#### الفهارس المطلوبة:

##### أ) فهرس أنظمة الري:
```
Collection: irrigation_systems
Fields:
- status (Ascending)
- userId (Ascending)  
- lastUpdate (Ascending)
- __name__ (Ascending)
```

##### ب) فهرس المنتجات:
```
Collection: products
Fields:
- category (Ascending)
- isAvailable (Ascending)
- createdAt (Descending)
```

##### ج) فهرس قراءات الحساسات:
```
Collection: sensor_readings
Fields:
- systemId (Ascending)
- timestamp (Descending)
```

##### د) فهرس سجل الري:
```
Collection: irrigation_logs
Fields:
- systemId (Ascending)
- timestamp (Descending)
```

### 3. **الحل التلقائي - Firebase CLI**

#### التثبيت:
```bash
npm install -g firebase-tools
firebase login
firebase init firestore
```

#### نشر الفهارس:
```bash
firebase deploy --only firestore:indexes
```

## 📁 ملف الفهارس المُعد مسبقاً

تم إنشاء ملف `firestore.indexes.json` يحتوي على جميع الفهارس المطلوبة:

```json
{
  "indexes": [
    {
      "collectionGroup": "irrigation_systems",
      "queryScope": "COLLECTION",
      "fields": [
        {"fieldPath": "status", "order": "ASCENDING"},
        {"fieldPath": "userId", "order": "ASCENDING"},
        {"fieldPath": "lastUpdate", "order": "ASCENDING"},
        {"fieldPath": "__name__", "order": "ASCENDING"}
      ]
    }
    // ... المزيد من الفهارس
  ]
}
```

## 🔧 استكشاف الأخطاء

### المشكلة: "Index creation failed"
**الحل:**
- تأكد من صحة أسماء الحقول
- تحقق من أن المجموعة موجودة
- انتظر بضع دقائق وحاول مرة أخرى

### المشكلة: "Permission denied"
**الحل:**
- تأكد من أن لديك صلاحيات المالك في المشروع
- تحقق من إعدادات IAM في Google Cloud Console

### المشكلة: "Index already exists"
**الحل:**
- تحقق من قائمة الفهارس الموجودة
- احذف الفهرس المكرر إذا لزم الأمر

## ⚡ نصائح للأداء

### 1. **تحسين الاستعلامات**
```dart
// بدلاً من
query.where('field1', isEqualTo: value1)
     .where('field2', isEqualTo: value2)
     .orderBy('field3');

// استخدم
query.where('field1', isEqualTo: value1)
     .orderBy('field3')
     .where('field2', isEqualTo: value2); // قد يحتاج فهرس مختلف
```

### 2. **تقليل عدد الفهارس**
- ادمج الاستعلامات المتشابهة
- استخدم الفهارس المفردة عند الإمكان
- تجنب الفهارس غير المستخدمة

### 3. **مراقبة الاستخدام**
- راقب استخدام الفهارس في Firebase Console
- احذف الفهارس غير المستخدمة
- راقب التكلفة والأداء

## 📊 حالة الفهارس الحالية

| الفهرس | الحالة | الوصف |
|---------|--------|-------|
| irrigation_systems (userId, status, lastUpdate) | ❌ مطلوب | للبحث في أنظمة الري |
| products (category, isAvailable, createdAt) | ❌ مطلوب | لعرض المنتجات |
| sensor_readings (systemId, timestamp) | ❌ مطلوب | لقراءات الحساسات |
| irrigation_logs (systemId, timestamp) | ❌ مطلوب | لسجل الري |

## 🎯 الخطوات التالية

1. **إنشاء الفهارس المطلوبة** باستخدام أحد الحلول أعلاه
2. **اختبار التطبيق** للتأكد من عمل الاستعلامات
3. **مراقبة الأداء** وتحسين الفهارس حسب الحاجة
4. **إضافة فهارس جديدة** عند إضافة ميزات جديدة

## 🔗 روابط مفيدة

- [Firebase Firestore Indexes Documentation](https://firebase.google.com/docs/firestore/query-data/indexing)
- [Firebase CLI Reference](https://firebase.google.com/docs/cli)
- [Firestore Pricing](https://firebase.google.com/pricing)

## ⚠️ تحذيرات مهمة

1. **التكلفة**: الفهارس تستهلك مساحة تخزين إضافية
2. **الوقت**: إنشاء الفهارس قد يستغرق عدة دقائق
3. **الحد الأقصى**: هناك حد أقصى لعدد الفهارس لكل مشروع
4. **الصيانة**: راجع الفهارس دورياً واحذف غير المستخدمة

---

*تم إعداد هذا الدليل لمشروع SAM05*  
*آخر تحديث: ديسمبر 2024*
