import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sam05/bloc/irrigation_bloc.dart';
import 'package:sam05/models/app_models.dart';
import 'package:intl/intl.dart';

/// Widget لعرض سجل الري
class IrrigationLogsWidget extends StatefulWidget {
  final String systemId;

  const IrrigationLogsWidget({
    super.key,
    required this.systemId,
  });

  @override
  State<IrrigationLogsWidget> createState() => _IrrigationLogsWidgetState();
}

class _IrrigationLogsWidgetState extends State<IrrigationLogsWidget> {
  @override
  void initState() {
    super.initState();
    // تحميل سجل الري عند بدء الWidget
    context.read<IrrigationBloc>().add(
      IrrigationEventLoadLogs(systemId: widget.systemId),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Row(
              children: [
                const Icon(Icons.history, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  'سجل الري',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: _refreshLogs,
                  tooltip: 'تحديث',
                ),
              ],
            ),
            const SizedBox(height: 16),

            // عرض السجل
            BlocBuilder<IrrigationBloc, IrrigationState>(
              builder: (context, state) {
                if (state is IrrigationStateLoading) {
                  return const Center(
                    child: Padding(
                      padding: EdgeInsets.all(20),
                      child: CircularProgressIndicator(),
                    ),
                  );
                }

                if (state is IrrigationStateLogsLoaded) {
                  if (state.logs.isEmpty) {
                    return _buildEmptyState();
                  }
                  return _buildLogsList(state.logs);
                }

                if (state is IrrigationStateError) {
                  return _buildErrorState(state.message);
                }

                return _buildEmptyState();
              },
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قائمة السجل
  Widget _buildLogsList(List<IrrigationLog> logs) {
    return SizedBox(
      height: 300,
      child: ListView.builder(
        itemCount: logs.length,
        itemBuilder: (context, index) {
          final log = logs[index];
          return _buildLogItem(log);
        },
      ),
    );
  }

  /// بناء عنصر السجل
  Widget _buildLogItem(IrrigationLog log) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _getLogStatusColor(log.status).withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _getLogStatusColor(log.status).withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // الصف الأول: النوع والحالة والوقت
          Row(
            children: [
              // نوع الري
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getTypeColor(log.type).withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _getTypeIcon(log.type),
                      size: 12,
                      color: _getTypeColor(log.type),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _getTypeName(log.type),
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                        color: _getTypeColor(log.type),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 8),
              
              // حالة الري
              Icon(
                _getStatusIcon(log.status),
                size: 16,
                color: _getLogStatusColor(log.status),
              ),
              const SizedBox(width: 4),
              Text(
                _getStatusName(log.status),
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: _getLogStatusColor(log.status),
                ),
              ),
              
              const Spacer(),
              
              // الوقت
              Text(
                _formatDateTime(log.startTime),
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          
          // الصف الثاني: المدة والمياه المستخدمة
          Row(
            children: [
              // المدة
              Row(
                children: [
                  const Icon(
                    Icons.timer,
                    size: 14,
                    color: Colors.blue,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${log.duration} دقيقة',
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              const SizedBox(width: 16),
              
              // المياه المستخدمة
              Row(
                children: [
                  const Icon(
                    Icons.water_drop,
                    size: 14,
                    color: Colors.cyan,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${log.waterUsed.toStringAsFixed(1)} لتر',
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              
              const Spacer(),
              
              // مصدر التشغيل
              if (log.triggeredBy != null)
                Text(
                  'بواسطة: ${_getTriggerName(log.triggeredBy!)}',
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey.shade500,
                  ),
                ),
            ],
          ),
          
          // الملاحظات (إن وجدت)
          if (log.notes != null && log.notes!.isNotEmpty) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.note,
                    size: 14,
                    color: Colors.grey.shade600,
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      log.notes!,
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Container(
      height: 200,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.history_outlined,
              size: 48,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 12),
            Text(
              'لا يوجد سجل ري',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'سيظهر سجل عمليات الري هنا',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState(String message) {
    return Container(
      height: 200,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.red.shade400,
            ),
            const SizedBox(height: 12),
            Text(
              'خطأ في تحميل السجل',
              style: TextStyle(
                fontSize: 16,
                color: Colors.red.shade600,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              message,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: _refreshLogs,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  // ===== دوال مساعدة =====

  void _refreshLogs() {
    context.read<IrrigationBloc>().add(
      IrrigationEventLoadLogs(systemId: widget.systemId),
    );
  }

  Color _getTypeColor(IrrigationType type) {
    switch (type) {
      case IrrigationType.manual:
        return Colors.blue;
      case IrrigationType.automatic:
        return Colors.orange;
      case IrrigationType.smart:
        return Colors.green;
    }
  }

  IconData _getTypeIcon(IrrigationType type) {
    switch (type) {
      case IrrigationType.manual:
        return Icons.touch_app;
      case IrrigationType.automatic:
        return Icons.schedule;
      case IrrigationType.smart:
        return Icons.psychology;
    }
  }

  String _getTypeName(IrrigationType type) {
    switch (type) {
      case IrrigationType.manual:
        return 'يدوي';
      case IrrigationType.automatic:
        return 'تلقائي';
      case IrrigationType.smart:
        return 'ذكي';
    }
  }

  Color _getLogStatusColor(IrrigationStatus status) {
    switch (status) {
      case IrrigationStatus.idle:
        return Colors.grey;
      case IrrigationStatus.running:
        return Colors.blue;
      case IrrigationStatus.paused:
        return Colors.orange;
      case IrrigationStatus.error:
        return Colors.red;
    }
  }

  IconData _getStatusIcon(IrrigationStatus status) {
    switch (status) {
      case IrrigationStatus.idle:
        return Icons.stop;
      case IrrigationStatus.running:
        return Icons.play_arrow;
      case IrrigationStatus.paused:
        return Icons.pause;
      case IrrigationStatus.error:
        return Icons.error;
    }
  }

  String _getStatusName(IrrigationStatus status) {
    switch (status) {
      case IrrigationStatus.idle:
        return 'مكتمل';
      case IrrigationStatus.running:
        return 'يعمل';
      case IrrigationStatus.paused:
        return 'متوقف مؤقتاً';
      case IrrigationStatus.error:
        return 'خطأ';
    }
  }

  String _getTriggerName(String trigger) {
    switch (trigger) {
      case 'manual':
        return 'يدوي';
      case 'automatic':
        return 'تلقائي';
      case 'smart':
        return 'ذكي';
      default:
        return trigger;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inDays > 0) {
      return DateFormat('dd/MM HH:mm').format(dateTime);
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}
