import "package:firebase_auth/firebase_auth.dart" as firebase_auth;
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/app_models.dart';

class AuthRepository {
  final firebase_auth.FirebaseAuth _firebaseAuth = firebase_auth.FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  Future<User> signIn({
    required String email,
    required String password,
  }) async {
    final userCredential = await _firebaseAuth.signInWithEmailAndPassword(
      email: email,
      password: password,
    );

    if (userCredential.user == null) {
      throw Exception('فشل تسجيل الدخول');
    }

    await _firestore.collection('users')
      .doc(userCredential.user!.uid)
      .update({
        'lastLogin': FieldValue.serverTimestamp(),
      });

    final userDoc = await _firestore.collection('users')
      .doc(userCredential.user!.uid)
      .get();

    if (!userDoc.exists) {
      throw Exception('لم يتم العثور على بيانات المستخدم');
    }

    return User.fromFirestore(userDoc);
  }






  Future<User> signUp({
    required String name,
    required String email,
    required String password,
  }) async {
    final userCredential = await _firebaseAuth.createUserWithEmailAndPassword(
      email: email,
      password: password,
    );

    if (userCredential.user == null) {
      throw Exception('فشل إنشاء الحساب');
    }

    await userCredential.user!.updateDisplayName(name);

    final now = DateTime.now();
    final user = User(
      id: userCredential.user!.uid,
      name: name,
      email: email,
      createdAt: now,
      lastLogin: now,
    );

    await _firestore.collection('users')
      .doc(user.id)
      .set(user.toFirestore());

    return user;
  }






  Future<void> signOut() async {
    await _firebaseAuth.signOut();
  }




  Future<void> resetPassword(String email) async {
    await _firebaseAuth.sendPasswordResetEmail(email: email);
  }





  Future<User?> getUserById(String id) async {
    final userDoc = await _firestore.collection('users')
      .doc(id)
      .get();

    if (!userDoc.exists) {
      return null;
    }

    return User.fromFirestore(userDoc);
  }





  Future<User> updateProfile(User user) async {
    await _firestore.collection('users')
      .doc(user.id)
      .update(user.toFirestore());

    return user;
  }




/*
  Future<User> updateUserImage(String userId, String imageUrl) async {
    await _firestore.collection('users')
      .doc(userId)
      .update({'imageUrl': imageUrl});

    final userDoc = await _firestore.collection('users')
      .doc(userId)
      .get();

    return User.fromFirestore(userDoc);
  }
*/



/*
  Future<void> changePassword(String newPassword) async {
    final user = _firebaseAuth.currentUser;

    if (user == null) {
      throw Exception('User not logged in');
    }

    await user.updatePassword(newPassword);
  }*/
}