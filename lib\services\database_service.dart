import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:sam05/core/constants/app_constants.dart';
import 'package:sam05/models/app_models.dart';


/// خدمة إدارة قاعدة البيانات وتهيئتها
class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// تهيئة قاعدة البيانات مع البيانات الأساسية
  Future<void> initializeDatabase() async {
    try {
      // التحقق من وجود أنواع النباتات الأساسية
      await _initializePlantTypes();
      
      // إنشاء الفهارس المطلوبة
      await _createIndexes();
      
      print('تم تهيئة قاعدة البيانات بنجاح');
    } catch (e) {
      print('خطأ في تهيئة قاعدة البيانات: $e');
      throw Exception('فشل في تهيئة قاعدة البيانات: $e');
    }
  }

  /// تهيئة أنواع النباتات الأساسية
  Future<void> _initializePlantTypes() async {
    try {
      // التحقق من وجود أنواع النباتات
      final snapshot = await _firestore
          .collection(AppConstants.collectionPlantTypes)
          .limit(1)
          .get();

      if (snapshot.docs.isEmpty) {
        // إضافة أنواع النباتات الأساسية
        final plantTypes = _getDefaultPlantTypes();
        
        final batch = _firestore.batch();
        for (final plantType in plantTypes) {
          final docRef = _firestore
              .collection(AppConstants.collectionPlantTypes)
              .doc();
          batch.set(docRef, plantType.toFirestore());
        }
        
        await batch.commit();
        print('تم إضافة أنواع النباتات الأساسية');
      }
    } catch (e) {
      print('خطأ في تهيئة أنواع النباتات: $e');
    }
  }

  /// الحصول على أنواع النباتات الافتراضية
  List<PlantType> _getDefaultPlantTypes() {
    final now = DateTime.now();
    
    return [
      PlantType(
        id: '',
        name: 'Tomato',
        arabicName: 'طماطم',
        description: 'نبات الطماطم من الخضروات الأساسية في الزراعة',
        category: 'vegetables',
        optimalMoisture: 0.6,
        minTemperature: 15.0,
        maxTemperature: 30.0,
        optimalPh: 6.5,
        growthDurationDays: 90,
        waterRequirementPerDay: 5.0,
        commonDiseases: ['اللفحة المبكرة', 'اللفحة المتأخرة', 'الذبول البكتيري'],
        irrigationTips: [
          'الري في الصباح الباكر',
          'تجنب الري على الأوراق',
          'الحفاظ على رطوبة التربة ثابتة'
        ],
        createdAt: now,
      ),
      PlantType(
        id: '',
        name: 'Cucumber',
        arabicName: 'خيار',
        description: 'نبات الخيار من الخضروات الصيفية المحبة للماء',
        category: 'vegetables',
        optimalMoisture: 0.7,
        minTemperature: 18.0,
        maxTemperature: 32.0,
        optimalPh: 6.0,
        growthDurationDays: 60,
        waterRequirementPerDay: 6.0,
        commonDiseases: ['البياض الدقيقي', 'الذبول الفطري', 'تبقع الأوراق'],
        irrigationTips: [
          'الري المنتظم والمتكرر',
          'تجنب جفاف التربة',
          'استخدام الري بالتنقيط'
        ],
        createdAt: now,
      ),
      PlantType(
        id: '',
        name: 'Wheat',
        arabicName: 'قمح',
        description: 'محصول القمح من الحبوب الأساسية',
        category: 'grains',
        optimalMoisture: 0.4,
        minTemperature: 10.0,
        maxTemperature: 25.0,
        optimalPh: 7.0,
        growthDurationDays: 120,
        waterRequirementPerDay: 3.0,
        commonDiseases: ['الصدأ الأصفر', 'الصدأ الأسود', 'التفحم'],
        irrigationTips: [
          'الري حسب مراحل النمو',
          'تقليل الري في مرحلة النضج',
          'مراقبة رطوبة التربة'
        ],
        createdAt: now,
      ),
      PlantType(
        id: '',
        name: 'Pepper',
        arabicName: 'فلفل',
        description: 'نبات الفلفل من الخضروات الحارة والحلوة',
        category: 'vegetables',
        optimalMoisture: 0.5,
        minTemperature: 20.0,
        maxTemperature: 35.0,
        optimalPh: 6.5,
        growthDurationDays: 80,
        waterRequirementPerDay: 4.0,
        commonDiseases: ['الذبول البكتيري', 'تبقع الأوراق', 'عفن الثمار'],
        irrigationTips: [
          'الري المعتدل والمنتظم',
          'تجنب الإفراط في الري',
          'الري في الصباح'
        ],
        createdAt: now,
      ),
      PlantType(
        id: '',
        name: 'Lettuce',
        arabicName: 'خس',
        description: 'نبات الخس من الخضروات الورقية',
        category: 'vegetables',
        optimalMoisture: 0.8,
        minTemperature: 12.0,
        maxTemperature: 22.0,
        optimalPh: 6.0,
        growthDurationDays: 45,
        waterRequirementPerDay: 4.5,
        commonDiseases: ['تعفن الجذور', 'البياض الدقيقي', 'حشرة المن'],
        irrigationTips: [
          'الري الخفيف والمتكرر',
          'الحفاظ على رطوبة عالية',
          'تجنب الري المباشر على الأوراق'
        ],
        createdAt: now,
      ),
    ];
  }

  /// إنشاء الفهارس المطلوبة لتحسين الأداء
  Future<void> _createIndexes() async {
    try {
      // ملاحظة: في التطبيق الحقيقي، يتم إنشاء الفهارس من خلال Firebase Console
      // أو باستخدام Firebase CLI، وليس من خلال الكود
      
      // هذه الدالة للتوثيق فقط لتوضيح الفهارس المطلوبة:
      
      // فهارس مجموعة irrigation_systems:
      // - userId (ascending)
      // - lastUpdate (descending)
      // - status (ascending)
      
      // فهارس مجموعة irrigation_records:
      // - systemId (ascending), startTime (descending)
      // - startTime (descending)
      
      // فهارس مجموعة sensor_readings:
      // - systemId (ascending), timestamp (descending)
      // - timestamp (descending)
      // - isAlert (ascending)
      
      // فهارس مجموعة water_usage_stats:
      // - systemId (ascending), date (descending)
      // - date (descending)
      
      // فهارس مجموعة plant_types:
      // - arabicName (ascending)
      // - category (ascending)
      
      print('تم التحقق من الفهارس المطلوبة');
    } catch (e) {
      print('خطأ في إنشاء الفهارس: $e');
    }
  }

  /// تنظيف البيانات القديمة
  Future<void> cleanupOldData() async {
    try {
      final cutoffDate = DateTime.now().subtract(const Duration(days: 90));
      
      // حذف قراءات الحساسات القديمة (أكثر من 90 يوم)
      final oldReadingsQuery = await _firestore
          .collection(AppConstants.collectionSensorReadings)
          .where('timestamp', isLessThan: Timestamp.fromDate(cutoffDate))
          .get();

      if (oldReadingsQuery.docs.isNotEmpty) {
        final batch = _firestore.batch();
        for (final doc in oldReadingsQuery.docs) {
          batch.delete(doc.reference);
        }
        await batch.commit();
        print('تم حذف ${oldReadingsQuery.docs.length} قراءة حساسات قديمة');
      }

      // حذف إحصائيات استخدام المياه القديمة (أكثر من 180 يوم)
      final oldStatsDate = DateTime.now().subtract(const Duration(days: 180));
      final oldStatsQuery = await _firestore
          .collection(AppConstants.collectionWaterUsageStats)
          .where('date', isLessThan: Timestamp.fromDate(oldStatsDate))
          .get();

      if (oldStatsQuery.docs.isNotEmpty) {
        final batch = _firestore.batch();
        for (final doc in oldStatsQuery.docs) {
          batch.delete(doc.reference);
        }
        await batch.commit();
        print('تم حذف ${oldStatsQuery.docs.length} إحصائية قديمة');
      }

    } catch (e) {
      print('خطأ في تنظيف البيانات القديمة: $e');
    }
  }

  /// إنشاء نسخة احتياطية من البيانات المهمة
  Future<void> backupCriticalData(String userId) async {
    try {
      // في التطبيق الحقيقي، يمكن تصدير البيانات إلى Cloud Storage
      // أو إرسالها عبر البريد الإلكتروني
      
      // جلب أنظمة الري للمستخدم
      final systemsSnapshot = await _firestore
          .collection(AppConstants.collectionIrrigationSystems)
          .where('userId', isEqualTo: userId)
          .get();

      final backupData = {
        'timestamp': DateTime.now().toIso8601String(),
        'userId': userId,
        'systems': systemsSnapshot.docs.map((doc) => {
          'id': doc.id,
          'data': doc.data(),
        }).toList(),
      };

      // في التطبيق الحقيقي، يتم حفظ البيانات في مكان آمن
      print('تم إنشاء نسخة احتياطية للمستخدم: $userId');
      print('حجم البيانات: ${(backupData['systems'] as List)?.length} نظام');
      
    } catch (e) {
      print('خطأ في إنشاء النسخة الاحتياطية: $e');
    }
  }

  /// التحقق من سلامة البيانات
  Future<Map<String, dynamic>> validateDataIntegrity() async {
    try {
      final results = <String, dynamic>{};
      
      // فحص أنظمة الري
      final systemsSnapshot = await _firestore
          .collection(AppConstants.collectionIrrigationSystems)
          .get();
      
      int validSystems = 0;
      int invalidSystems = 0;
      
      for (final doc in systemsSnapshot.docs) {
        try {
          IrrigationSystem.fromFirestore(doc);
          validSystems++;
        } catch (e) {
          invalidSystems++;
        }
      }
      
      results['irrigation_systems'] = {
        'total': systemsSnapshot.docs.length,
        'valid': validSystems,
        'invalid': invalidSystems,
      };

      // فحص سجلات الري
      final recordsSnapshot = await _firestore
          .collection(AppConstants.collectionIrrigationRecords)
          .limit(100)
          .get();
      
      int validRecords = 0;
      int invalidRecords = 0;
      
      for (final doc in recordsSnapshot.docs) {
        try {
          IrrigationRecord.fromFirestore(doc);
          validRecords++;
        } catch (e) {
          invalidRecords++;
        }
      }
      
      results['irrigation_records'] = {
        'checked': recordsSnapshot.docs.length,
        'valid': validRecords,
        'invalid': invalidRecords,
      };

      results['status'] = 'completed';
      results['timestamp'] = DateTime.now().toIso8601String();
      
      return results;
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }
}
