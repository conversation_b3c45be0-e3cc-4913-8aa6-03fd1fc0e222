import 'package:flutter/material.dart';
import 'package:sam05/models/app_models.dart';
import 'package:sam05/core/utils/irrigation_utils.dart';

/// Widget لعرض معلومات النظام المحدد
class SystemInfoWidget extends StatelessWidget {
  final IrrigationSystem system;
  final IrrigationSettings? settings;
  final VoidCallback? onEditSystem;
  final VoidCallback? onDeleteSystem;

  const SystemInfoWidget({
    super.key,
    required this.system,
    this.settings,
    this.onEditSystem,
    this.onDeleteSystem,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان والإجراءات
            Row(
              children: [
                Icon(
                  IrrigationUtils.getSystemTypeIcon(system.type),
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        system.name,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        IrrigationUtils.getSystemTypeName(system.type),
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                // مؤشر الاتصال
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: system.isOnline ? Colors.green : Colors.red,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        system.isOnline ? Icons.wifi : Icons.wifi_off,
                        color: Colors.white,
                        size: 14,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        system.isOnline ? 'متصل' : 'غير متصل',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                // قائمة الإجراءات
                PopupMenuButton<String>(
                  onSelected: (value) {
                    switch (value) {
                      case 'edit':
                        onEditSystem?.call();
                        break;
                      case 'delete':
                        _showDeleteConfirmation(context);
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, size: 18),
                          SizedBox(width: 8),
                          Text('تعديل'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, size: 18, color: Colors.red),
                          SizedBox(width: 8),
                          Text('حذف', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // معلومات النظام
            _buildInfoGrid(context),
            
            if (settings != null) ...[
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 16),
              _buildSettingsInfo(context, settings!),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء شبكة المعلومات
  Widget _buildInfoGrid(BuildContext context) {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 3,
      crossAxisSpacing: 16,
      mainAxisSpacing: 12,
      children: [
        _buildInfoItem(
          context,
          'الرقم التسلسلي',
          system.serialNumber,
          Icons.qr_code,
        ),
        _buildInfoItem(
          context,
          'الحالة',
          IrrigationUtils.getSystemStatusName(system.status.toString().split('.').last),
          IrrigationUtils.getSystemStatusIcon(system.status.toString().split('.').last),
          color: IrrigationUtils.getSystemStatusColor(system.status.toString().split('.').last),
        ),
        _buildInfoItem(
          context,
          'الموقع',
          system.location ?? 'غير محدد',
          Icons.location_on,
        ),
        _buildInfoItem(
          context,
          'نوع المحصول',
          system.cropType ?? 'غير محدد',
          Icons.agriculture,
        ),
        _buildInfoItem(
          context,
          'آخر تحديث',
          _formatDateTime(system.lastUpdate),
          Icons.access_time,
        ),
        _buildInfoItem(
          context,
          'تاريخ الإنشاء',
          _formatDateTime(system.createdAt),
          Icons.calendar_today,
        ),
      ],
    );
  }

  /// بناء عنصر معلومة
  Widget _buildInfoItem(
    BuildContext context,
    String label,
    String value,
    IconData icon, {
    Color? color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            size: 18,
            color: color ?? Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.bold,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء معلومات الإعدادات
  Widget _buildSettingsInfo(BuildContext context, IrrigationSettings settings) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إعدادات الري',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        
        // أوضاع الري المفعلة
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            if (settings.manualModeEnabled)
              _buildModeChip('يدوي', Icons.touch_app, Colors.blue),
            if (settings.autoModeEnabled)
              _buildModeChip('تلقائي', Icons.auto_mode, Colors.green),
            if (settings.smartModeEnabled)
              _buildModeChip('ذكي', Icons.psychology, Colors.purple),
          ],
        ),
        const SizedBox(height: 12),
        
        // معلومات الإعدادات
        Row(
          children: [
            Expanded(
              child: _buildSettingItem(
                'عتبة الرطوبة',
                '${(settings.moistureThreshold * 100).toInt()}%',
                Icons.water_drop,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildSettingItem(
                'المدة الافتراضية',
                '${settings.defaultDuration} دقيقة',
                Icons.timer,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء شريحة الوضع
  Widget _buildModeChip(String label, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر إعداد
  Widget _buildSettingItem(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Colors.grey.shade600),
          const SizedBox(width: 6),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey.shade600,
                  ),
                ),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// عرض تأكيد الحذف
  void _showDeleteConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف نظام "${system.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              onDeleteSystem?.call();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  /// تنسيق التاريخ والوقت
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
