import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:sam05/models/app_models.dart';

class EditIrrigationSystemScreen extends StatefulWidget {
  final IrrigationSystem? initialSystem;

  const EditIrrigationSystemScreen({super.key, this.initialSystem});

  @override
  State<EditIrrigationSystemScreen> createState() =>
      _EditIrrigationSystemScreenState();
}

class _EditIrrigationSystemScreenState
    extends State<EditIrrigationSystemScreen> {
  final _formKey = GlobalKey<FormBuilderState>();
  bool _isEditMode = false;

  @override
  void initState() {
    super.initState();
    _isEditMode = widget.initialSystem != null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditMode ? 'تعديل نظام الري' : 'إضافة نظام ري جديد'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: FormBuilder(
          key: _formKey,
          initialValue: _isEditMode
              ? {
                  'name': widget.initialSystem!.name,
                  'type': widget.initialSystem!.type,
                }
              : {},
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildFormFields(),
              const SizedBox(height: 32),
              ElevatedButton.icon(
                onPressed: _submitForm,
                icon: const Icon(Icons.save),
                label: const Text('حفظ النظام'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFormFields() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات النظام',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            FormBuilderTextField(
              name: 'name',
              decoration: const InputDecoration(
                labelText: 'اسم النظام',
                hintText: 'مثال: حقل الطماطم الرئيسي',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.label),
              ),
              validator: FormBuilderValidators.compose([
                FormBuilderValidators.required(errorText: 'اسم النظام مطلوب.'),
                FormBuilderValidators.minLength(3, errorText: 'الاسم قصير جداً.'),
              ]),
            ),
            const SizedBox(height: 16),
            FormBuilderDropdown<String>(
              name: 'type',
              decoration: const InputDecoration(
                labelText: 'نوع نظام الري',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.water_drop),
              ),
              items: const [
                DropdownMenuItem(
                  value: 'drip',
                  child: Text('ري بالتنقيط'),
                ),
                DropdownMenuItem(
                  value: 'sprinkler',
                  child: Text('ري بالرش'),
                ),
                DropdownMenuItem(
                  value: 'micro',
                  child: Text('ري دقيق'),
                ),
              ],
              validator: FormBuilderValidators.required(
                  errorText: 'يجب اختيار نوع النظام.'),
            ),
          ],
        ),
      ),
    );
  }

  void _submitForm() {
    if (_formKey.currentState?.saveAndValidate() ?? false) {
      final formData = _formKey.currentState!.value;
      // هنا سيتم إرسال البيانات إلى BLoC لحفظها
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم حفظ النظام: ${formData['name']}'),
          backgroundColor: Colors.green,
        ),
      );
      Navigator.of(context).pop();
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى تصحيح الأخطاء في النموذج'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}