import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
import '../models/app_models.dart';

class MarketRepository {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final Uuid _uuid = const Uuid();

  Future<List<Product>> getProducts() async {
    final snapshot = await _firestore.collection('products').get();

    final products =
        snapshot.docs.map((doc) => Product.fromFirestore(doc)).toList();
    products.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    return products;
  }

  Future<Product?> getProductById(String id) async {
    final doc = await _firestore.collection('products').doc(id).get();

    if (!doc.exists) {
      return null;
    }

    return Product.fromFirestore(doc);
  }

  Future<List<Product>> getUserProducts(String userId) async {
    try {
      final snapshot = await _firestore
          .collection('products')
          .where('sellerId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs.map((doc) => Product.fromFirestore(doc)).toList();
    } on FirebaseException catch (e) {
      if (e.code == 'failed-precondition') {}
      rethrow;
    }
  }

  Future<List<String>> uploadProductImages(List<TempFileData> images) async {
    final imageUrls = <String>[];

    for (var image in images) {
      final fileName = '${_uuid.v4()}.jpg';
      final ref = _storage.ref().child('product_images/$fileName');

      final metadata = SettableMetadata(
        contentType: image.mimeType,
      );

      final uploadTask = ref.putData(image.bytes, metadata);
      final snapshot = await uploadTask.whenComplete(() => null);

      final downloadUrl = await snapshot.ref.getDownloadURL();
      imageUrls.add(downloadUrl);
    }

    return imageUrls;
  }

  Future<Product> addProduct({
    required String name,
    required String description,
    required double price,
    required String unit,
    required double quantity,
    required String category,
    required List<String> imageUrls,
    required String location,
    required String sellerId,
    required String sellerName,
    String? sellerPhone,
  }) async {
    final docRef = await _firestore.collection('products').add({
      'name': name,
      'description': description,
      'price': price,
      'unit': unit,
      'quantity': quantity,
      'category': category,
      'imageUrls': imageUrls,
      'location': location,
      'createdAt': FieldValue.serverTimestamp(),
      'sellerId': sellerId,
      'sellerName': sellerName,
      'sellerPhone': sellerPhone,
    });

    final doc = await docRef.get();
    return Product.fromFirestore(doc);
  }

  Future<Product> updateProduct({
    required String id,
    required String name,
    required String description,
    required double price,
    required String unit,
    required double quantity,
    required String category,
    required List<String> imageUrls,
    required String location,
    String? sellerPhone,
  }) async {
    await _firestore.collection('products').doc(id).update({
      'name': name,
      'description': description,
      'price': price,
      'unit': unit,
      'quantity': quantity,
      'category': category,
      'imageUrls': imageUrls,
      'location': location,
      'sellerPhone': sellerPhone,
    });

    final doc = await _firestore.collection('products').doc(id).get();
    return Product.fromFirestore(doc);
  }

  Future<void> deleteProduct(String id) async {
    await _firestore.collection('products').doc(id).delete();
  }

  Future<List<Chat>> getChats(String userId) async {
    try {
      final snapshot = await _firestore
          .collection('chats')
          .where('participants', arrayContains: userId)
          .get();

      final chats =
          snapshot.docs.map((doc) => Chat.fromFirestore(doc)).toList();
      chats.sort((a, b) => b.lastMessageTime.compareTo(a.lastMessageTime));
      return chats;
    } catch (e) {
      rethrow;
    }
  }

  Future<List<Message>> getMessages(String chatId) async {
    try {
      final snapshot = await _firestore
          .collection('messages')
          .where('chatId', isEqualTo: chatId)
          .get();

      final messages =
          snapshot.docs.map((doc) => Message.fromFirestore(doc)).toList();
      messages.sort((a, b) => a.timestamp.compareTo(b.timestamp));
      return messages;
    } catch (e) {
      rethrow;
    }
  }

//-------------------------------- رفع الملفات مدفوع يحتاج بطاقة فيزا------------------------------

  Future<List<String>> uploadMessageAttachments(
      List<TempFileData> attachments, String chatId) async {
    final attachmentUrls = <String>[];

    for (var attachment in attachments) {
      final fileName = '${_uuid.v4()}.jpg';
      final ref = _storage.ref().child('message_attachments/$chatId/$fileName');
// التعرف على نوع الملف المرفق
      final metadata = SettableMetadata(
        contentType: attachment.mimeType,
      );
//رفع الملفات
      final uploadTask = ref.putData(attachment.bytes, metadata);
      final snapshot = await uploadTask.whenComplete(() => null);

      final downloadUrl = await snapshot.ref.getDownloadURL();
      attachmentUrls.add(downloadUrl);
    }

    return attachmentUrls;
  }

  /// إرسال رسالة جديدة أو إنشاء محادثة جديدة
  Future<Message> sendMessage({
    required String chatId,
    required String content,
    required String senderId,
    required List<String> participants,
    List<String>? attachmentUrls,
    String? attachmentType,
  }) async {
    var actualChatId = chatId;

    if (chatId == 'new') {
      final chatRef = await _firestore.collection('chats').add({
        'participants': participants,
        'lastMessage': content,
        'lastMessageTime': FieldValue.serverTimestamp(),
        'lastSenderId': senderId,
        'unreadCount': participants.fold<Map<String, dynamic>>(
          {},
          (map, participant) {
            if (participant != senderId) {
              map[participant] = 1;
            } else {
              map[participant] = 0;
            }
            return map;
          },
        ),
      });
//معرف
      actualChatId = chatRef.id;
    } else {
      final chatDoc = await _firestore.collection('chats').doc(chatId).get();
//نتاكد هل المحادثة موجود ونحولها عشان نشتغل معاها
      if (chatDoc.exists) {
        final data = chatDoc.data() as Map<String, dynamic>;

        Map<String, dynamic> unreadCount = {};
//map  للرسائل غير المقرؤة
        final existingUnreadCount = data['unreadCount'];
        if (existingUnreadCount is Map) {
          unreadCount = Map<String, dynamic>.from(existingUnreadCount);
        }
//فوريه تمر على جميع المشاركين  وتزيد عدد الرسائل غير المقرؤة بواحد
        for (var participant in participants) {
          if (participant != senderId) {
            unreadCount[participant] = (unreadCount[participant] ?? 0) + 1;
          }
        }

        await _firestore.collection('chats').doc(chatId).update({
          'lastMessage': content,
          'lastMessageTime': FieldValue.serverTimestamp(),
          'lastSenderId': senderId,
          'unreadCount': unreadCount,
        });
      } else {
        //لو مش موجود ننشئها
        await _firestore.collection('chats').doc(chatId).set({
          'participants': participants,
          'lastMessage': content,
          'lastMessageTime': FieldValue.serverTimestamp(),
          'lastSenderId': senderId,
          'unreadCount': participants.fold<Map<String, dynamic>>(
            {},
            (map, participant) {
              if (participant != senderId) {
                map[participant] = 1;
              } else {
                map[participant] = 0;
              }
              return map;
            },
          ),
        });
      }
    }
//نضيفها كمحادثة جديدة
    final messageRef = await _firestore.collection('messages').add({
      'chatId': actualChatId,
      'senderId': senderId,
      'content': content,
      'timestamp': FieldValue.serverTimestamp(),
      'isRead': false,
      'attachmentUrls': attachmentUrls,
      'attachmentType': attachmentType,
    });

    final messageDoc = await messageRef.get();
    return Message.fromFirestore(messageDoc);
  }

  Future<void> markMessagesAsRead(String chatId, String userId) async {
    try {
      final chatDoc = await _firestore.collection('chats').doc(chatId).get();

      if (chatDoc.exists) {
        try {
          final data = chatDoc.data() as Map<String, dynamic>;
          Map<String, dynamic> unreadCount = {};

          final existingUnreadCount = data['unreadCount'];
          if (existingUnreadCount is Map) {
            unreadCount = Map<String, dynamic>.from(existingUnreadCount);
          }

          unreadCount[userId] = 0;

          await _firestore.collection('chats').doc(chatId).update({
            'unreadCount': unreadCount,
          });
        } catch (e) {
          // تسجيل الخطأ في تحديث عدد الرسائل غير المقروءة
          debugPrint('Failed to update unread count: $e');
        }
      }

      final messagesQuery = await _firestore
          .collection('messages')
          .where('chatId', isEqualTo: chatId)
          .get();

      final batch = _firestore.batch();
      var hasUpdates = false;

      for (var doc in messagesQuery.docs) {
        final data = doc.data();
        if (data['senderId'] != userId && data['isRead'] == false) {
          batch.update(doc.reference, {'isRead': true});
          hasUpdates = true;
        }
      }

      if (hasUpdates) {
        await batch.commit();
      }
    } catch (e) {
      // تسجيل الخطأ في تحديث حالة قراءة الرسائل
      debugPrint('Failed to mark messages as read: $e');
    }
  }
}
